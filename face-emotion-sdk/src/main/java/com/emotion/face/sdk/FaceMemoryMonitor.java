package com.emotion.face.sdk;

import android.util.Log;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 人脸模块内存监控器
 * 
 * 功能：
 * - 监控FaceBoxPool的使用情况
 * - 监控ImageProcessHelper的池状态
 * - 提供人脸模块专用的内存报告
 * - 人脸模块自动优化建议
 */
public class FaceMemoryMonitor {
    
    private static final String TAG = "FaceMemoryMonitor";
    
    // 监控周期
    private static final long REPORT_INTERVAL = 30000; // 30秒
    
    // 统计信息
    private static final AtomicLong lastReportTime = new AtomicLong(0);
    private static final AtomicLong totalReports = new AtomicLong(0);
    
    /**
     * 生成人脸模块内存报告
     */
    public static String generateMemoryReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 人脸模块内存报告 ===\n");
        
        // 1. FaceBox对象池状态
        report.append("1. FaceBox对象池:\n");
        report.append("   ").append(FaceBoxPool.getStats()).append("\n");
        report.append("   健康状态: ").append(FaceBoxPool.isHealthy() ? "健康" : "需要优化").append("\n");
        
        // 2. ImageProcessHelper池状态
        report.append("2. 图像处理池:\n");
        report.append("   ").append(ImageProcessHelper.getPoolStats()).append("\n");
        
        // 3. 人脸模块内存使用
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        report.append("3. 系统内存状态:\n");
        report.append("   使用内存: ").append(formatBytes(usedMemory)).append("\n");
        report.append("   内存使用率: ").append(String.format("%.1f%%", (double) usedMemory / maxMemory * 100)).append("\n");
        
        // 4. 人脸模块优化建议
        report.append("4. 优化建议:\n");
        report.append(generateOptimizationAdvice());
        
        report.append("========================\n");
        
        totalReports.incrementAndGet();
        lastReportTime.set(System.currentTimeMillis());
        
        return report.toString();
    }
    
    /**
     * 生成人脸模块优化建议
     */
    private static String generateOptimizationAdvice() {
        StringBuilder advice = new StringBuilder();
        
        // 检查FaceBox池健康状态
        if (!FaceBoxPool.isHealthy()) {
            advice.append("   - FaceBox池命中率过低，考虑预热或增加池大小\n");
        }
        
        // 检查内存使用率
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        double memoryUsage = (double) usedMemory / maxMemory;
        
        if (memoryUsage > 0.8) {
            advice.append("   - 内存使用率过高(").append(String.format("%.1f%%", memoryUsage * 100)).append(")，建议优化人脸检测频率\n");
        } else if (memoryUsage > 0.6) {
            advice.append("   - 内存使用率较高(").append(String.format("%.1f%%", memoryUsage * 100)).append(")，注意监控\n");
        }
        
        if (advice.length() == 0) {
            advice.append("   - 人脸模块内存使用状况良好\n");
        }
        
        return advice.toString();
    }
    
    /**
     * 格式化字节数
     */
    private static String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * 人脸模块定期监控检查
     */
    public static void performPeriodicCheck() {
        long currentTime = System.currentTimeMillis();
        long lastReport = lastReportTime.get();
        
        if (currentTime - lastReport >= REPORT_INTERVAL) {
            String report = generateMemoryReport();
            Log.i(TAG, report);
            
            // 检查是否需要优化
            if (shouldOptimizePools()) {
                optimizeFacePools();
            }
        }
    }
    
    /**
     * 检查是否需要优化人脸模块对象池
     */
    private static boolean shouldOptimizePools() {
        // 检查FaceBox池健康状态
        if (!FaceBoxPool.isHealthy()) {
            return true;
        }
        
        // 检查内存使用率
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        double memoryUsage = (double) usedMemory / maxMemory;
        
        return memoryUsage > 0.7; // 内存使用率超过70%时进行优化
    }
    
    /**
     * 优化人脸模块对象池
     */
    public static void optimizeFacePools() {
        Log.i(TAG, "开始优化人脸模块对象池");
        
        // 优化FaceBox池
        FaceBoxPool.optimize();
        
        Log.i(TAG, "人脸模块对象池优化完成");
    }
    
    /**
     * 预热人脸模块对象池
     */
    public static void warmupFacePools() {
        Log.i(TAG, "开始预热人脸模块对象池");
        
        // 预热FaceBox池
        FaceBoxPool.warmup();
        
        Log.i(TAG, "人脸模块对象池预热完成");
    }
    
    /**
     * 清理人脸模块对象池
     */
    public static void cleanupFacePools() {
        Log.i(TAG, "开始清理人脸模块对象池");
        
        // 清理FaceBox池
        FaceBoxPool.cleanup();
        
        // 清理ImageProcessHelper池
        ImageProcessHelper.cleanup();
        
        Log.i(TAG, "人脸模块对象池清理完成");
    }
    
    /**
     * 快速状态检查
     */
    public static String getQuickStatus() {
        return String.format("人脸模块内存状态: FaceBox=%s, ImageProcess=%s", 
            FaceBoxPool.isHealthy() ? "健康" : "异常",
            "正常" // ImageProcessHelper没有健康检查
        );
    }
    
    /**
     * 获取人脸模块统计信息
     */
    public static String getStatistics() {
        return String.format("人脸模块监控统计: 总报告=%d次, 最后报告=%d秒前", 
            totalReports.get(),
            (System.currentTimeMillis() - lastReportTime.get()) / 1000
        );
    }
    
    /**
     * 获取详细的人脸模块状态
     */
    public static String getDetailedStatus() {
        return String.format(
            "=== 人脸模块详细状态 ===\n" +
            "%s\n" +
            "%s\n" +
            "%s\n" +
            "=========================",
            FaceBoxPool.getDetailedStats(),
            ImageProcessHelper.getPoolStats(),
            getStatistics()
        );
    }
} 