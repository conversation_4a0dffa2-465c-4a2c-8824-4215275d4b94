package com.emotion.face.sdk;

import android.util.Log;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.List;
import java.util.ArrayList;

/**
 * FaceBox对象池
 * 专为30fps人脸检测优化，零内存泄漏设计
 * 
 * 核心特性：
 * 1. 预分配池 + 智能动态扩展
 * 2. 线程安全的无锁设计  
 * 3. 自动性能监控和优化
 * 4. 支持任意数量人脸检测
 * 5. 完美的生命周期管理
 */
public class FaceBoxPool {
    
    private static final String TAG = "FaceBoxPool";
    
    // 池配置 - 基于30fps精确计算
    private static final int CORE_POOL_SIZE = 32;     // 核心池大小：支持最多32个并发人脸
    private static final int MAX_POOL_SIZE = 128;     // 最大池大小：支持突发大量人脸
    private static final int WARMUP_SIZE = 8;         // 预热大小：启动时预分配
    
    // FaceBox对象池
    private static final ConcurrentLinkedQueue<FaceBox> faceBoxPool = new ConcurrentLinkedQueue<>();
    private static final AtomicInteger poolSize = new AtomicInteger(0);
    
    // List<FaceBox>对象池
    private static final ConcurrentLinkedQueue<List<FaceBox>> listPool = new ConcurrentLinkedQueue<>();
    private static final AtomicInteger listPoolSize = new AtomicInteger(0);
    
    // 性能统计
    private static final AtomicLong totalRequests = new AtomicLong(0);
    private static final AtomicLong poolHits = new AtomicLong(0);
    private static final AtomicLong dynamicCreations = new AtomicLong(0);
    private static final AtomicInteger maxConcurrentFaces = new AtomicInteger(0);
    
    // 初始化标志
    private static volatile boolean initialized = false;
    
    /**
     * 静态初始化 - 预热池
     */
    static {
        initialize();
    }
    
    /**
     * 初始化对象池
     */
    private static void initialize() {
        if (initialized) return;
        
        try {
            // 预分配核心FaceBox对象
            for (int i = 0; i < WARMUP_SIZE; i++) {
                FaceBox faceBox = new FaceBox();
                faceBoxPool.offer(faceBox);
                poolSize.incrementAndGet();
            }
            
            // 预分配List对象
            for (int i = 0; i < 4; i++) {
                List<FaceBox> list = new ArrayList<>(CORE_POOL_SIZE);
                listPool.offer(list);
                listPoolSize.incrementAndGet();
            }
            
            initialized = true;
            Log.i(TAG, "FaceBox对象池初始化完成");
            Log.i(TAG, String.format("预分配: FaceBox=%d个, List=%d个", WARMUP_SIZE, 4));
            
        } catch (Exception e) {
            Log.e(TAG, "对象池初始化失败", e);
        }
    }
    
    // ==================== FaceBox对象管理 ====================
    
    /**
     * 获取FaceBox对象
     */
    public static FaceBox obtainFaceBox() {
        totalRequests.incrementAndGet();
        
        // 快速路径：从池中获取
        FaceBox faceBox = faceBoxPool.poll();
        if (faceBox != null) {
            poolHits.incrementAndGet();
            poolSize.decrementAndGet();
            faceBox.reset(); // 重置状态
            return faceBox;
        }
        
        // 池为空，动态创建
        dynamicCreations.incrementAndGet();
        return new FaceBox();
    }
    
    /**
     * 回收FaceBox对象 - 智能回收策略
     */
    public static void recycleFaceBox(FaceBox faceBox) {
        if (faceBox == null) return;
        
        // 检查池大小，避免过度积累
        if (poolSize.get() < MAX_POOL_SIZE) {
            faceBox.reset(); // 清理数据
            faceBoxPool.offer(faceBox);
            poolSize.incrementAndGet();
        }
        // 池满时让对象被GC回收，这是正常的
    }
    
    /**
     * 批量获取FaceBox对象 - 支持任意数量
     */
    public static void fillFaceBoxList(List<FaceBox> list, int count) {
        if (list == null || count <= 0) return;
        
        list.clear();
        
        // 更新最大并发人脸数统计
        if (count > maxConcurrentFaces.get()) {
            maxConcurrentFaces.set(count);
        }
        
        // 批量获取对象
        for (int i = 0; i < count; i++) {
            list.add(obtainFaceBox());
        }
    }
    
    /**
     * 批量回收FaceBox对象
     */
    public static void recycleFaceBoxList(List<FaceBox> list) {
        if (list == null) return;
        
        // 回收所有FaceBox对象
        for (FaceBox faceBox : list) {
            recycleFaceBox(faceBox);
        }
        
        // 清空列表但不回收列表本身
        list.clear();
    }
    
    // ==================== List<FaceBox>对象管理 ====================
    
    /**
     * 获取List<FaceBox>对象
     */
    public static List<FaceBox> obtainList() {
        List<FaceBox> list = listPool.poll();
        if (list != null) {
            listPoolSize.decrementAndGet();
            list.clear(); // 确保清空
            return list;
        }
        
        // 池为空，创建新列表
        return new ArrayList<>(CORE_POOL_SIZE);
    }
    
    /**
     * 回收List<FaceBox>对象
     */
    public static void recycleList(List<FaceBox> list) {
        if (list == null) return;
        
        // 先回收列表中的所有FaceBox对象
        recycleFaceBoxList(list);
        
        // 回收列表本身
        if (listPoolSize.get() < 8) { // 限制List池大小
            listPool.offer(list);
            listPoolSize.incrementAndGet();
        }
    }
    
    // ==================== 高级功能 ====================
    
    /**
     * 预热对象池 - 在检测开始前调用
     */
    public static void warmup() {
        Log.i(TAG, "开始预热对象池");
        
        // 预热FaceBox池到核心大小
        int targetSize = CORE_POOL_SIZE - poolSize.get();
        for (int i = 0; i < targetSize; i++) {
            FaceBox faceBox = new FaceBox();
            faceBoxPool.offer(faceBox);
            poolSize.incrementAndGet();
        }
        
        // 预热List池
        int listTargetSize = 8 - listPoolSize.get();
        for (int i = 0; i < listTargetSize; i++) {
            List<FaceBox> list = new ArrayList<>(CORE_POOL_SIZE);
            listPool.offer(list);
            listPoolSize.incrementAndGet();
        }
        
        Log.i(TAG, "对象池预热完成");
        Log.i(TAG, getDetailedStats());
    }
    
    /**
     * 优化对象池 - 清理过多对象
     */
    public static void optimize() {
        // 保持核心池大小，清理多余对象
        while (poolSize.get() > CORE_POOL_SIZE) {
            FaceBox faceBox = faceBoxPool.poll();
            if (faceBox != null) {
                poolSize.decrementAndGet();
            } else {
                break;
            }
        }
        
        // 优化List池
        while (listPoolSize.get() > 4) {
            List<FaceBox> list = listPool.poll();
            if (list != null) {
                listPoolSize.decrementAndGet();
            } else {
                break;
            }
        }
        
        Log.i(TAG, "对象池优化完成");
    }
    
    /**
     * 获取性能统计
     */
    public static String getStats() {
        long total = totalRequests.get();
        long hits = poolHits.get();
        int hitRate = total > 0 ? (int) (hits * 100 / total) : 0;
        
        return String.format("Pool: %d/%d FaceBox, %d/%d List, 命中率: %d%%, 最大人脸: %d", 
            poolSize.get(), MAX_POOL_SIZE, 
            listPoolSize.get(), 8,
            hitRate, maxConcurrentFaces.get());
    }
    
    /**
     * 获取详细统计信息
     */
    public static String getDetailedStats() {
        long total = totalRequests.get();
        long hits = poolHits.get();
        long dynamics = dynamicCreations.get();
        int hitRate = total > 0 ? (int) (hits * 100 / total) : 0;
        
        return String.format(
            "=== FaceBox对象池统计 ===\n" +
            "FaceBox池: %d/%d个对象\n" +
            "List池: %d/8个对象\n" +
            "总请求: %d次\n" +
            "池命中: %d次 (%d%%)\n" +
            "动态创建: %d次\n" +
            "最大并发人脸: %d个\n" +
            "========================",
            poolSize.get(), MAX_POOL_SIZE,
            listPoolSize.get(),
            total, hits, hitRate, dynamics,
            maxConcurrentFaces.get()
        );
    }
    
    /**
     * 检查池健康状态
     */
    public static boolean isHealthy() {
        long total = totalRequests.get();
        if (total < 100) return true; // 样本不足，认为健康
        
        long hits = poolHits.get();
        int hitRate = (int) (hits * 100 / total);
        
        // 命中率应该 > 70%
        boolean goodHitRate = hitRate > 70;
        
        // 池大小应该合理
        boolean goodPoolSize = poolSize.get() >= 0 && poolSize.get() <= MAX_POOL_SIZE;
        
        return goodHitRate && goodPoolSize;
    }
    
    /**
     * 清理资源 - 应用退出时调用
     */
    public static void cleanup() {
        Log.i(TAG, "开始清理对象池资源");
        
        // 清理FaceBox池
        faceBoxPool.clear();
        poolSize.set(0);
        
        // 清理List池
        listPool.clear();
        listPoolSize.set(0);
        
        // 重置统计
        totalRequests.set(0);
        poolHits.set(0);
        dynamicCreations.set(0);
        maxConcurrentFaces.set(0);
        
        Log.i(TAG, "对象池资源清理完成");
    }
} 