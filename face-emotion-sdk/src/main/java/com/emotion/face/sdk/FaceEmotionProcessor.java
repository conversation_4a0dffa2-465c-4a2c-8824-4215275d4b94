package com.emotion.face.sdk;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.Log;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 面部情感处理器
 * 专门用于处理外部传入的图像帧，不包含摄像头管理功能
 * 
 * 设计理念：
 * - 纯粹的人脸检测和情感分析引擎
 * - 完全分离摄像头管理和检测逻辑
 * - 支持任意来源的图像数据
 * - 保持与 FaceEmotionModule 相同的检测能力和回调接口
 */
public class FaceEmotionProcessor {
    private static final String TAG = "FaceEmotionProcessor";
    
    // 状态枚举
    private enum ProcessorState {
        UNINITIALIZED,
        INITIALIZING,
        INITIALIZED,
        RUNNING,
        STOPPED,
        ERROR
    }
    
    private final Context context;
    private final FaceEmotionInterface emotionInterface;
    private final int batchFrames;
    
    // 核心组件（不包含摄像头管理）
    private FaceDetectionService detectionService;
    private FaceSimpleBatchProcessor batchProcessor;
    private FaceProcessorCallback callback;
    
    // 可复用的检测结果List - 避免频繁创建
    private final List<FaceBox> reusableResultList = new ArrayList<>();
    
    // 性能统计
    private long totalFrames = 0;
    private long optimizedDetectionTime = 0;
    private long lastPerformanceReport = System.currentTimeMillis();
    
    // 使用原子状态管理
    private final AtomicInteger processorState = new AtomicInteger(ProcessorState.UNINITIALIZED.ordinal());
    private final AtomicBoolean isProcessing = new AtomicBoolean(false);
    
    /**
     * 构造函数
     * 
     * @param context 应用上下文
     * @param batchFrames 批次处理的帧数
     */
    public FaceEmotionProcessor(Context context, int batchFrames) {
        this.context = context;
        this.batchFrames = batchFrames;
        this.emotionInterface = new FaceEmotionInterface();
        
        Log.i(TAG, "FaceEmotionProcessor创建完成，批次帧数: " + batchFrames);
    }
    
    /**
     * 设置回调
     */
    public void setCallback(FaceProcessorCallback callback) {
        this.callback = callback;
    }
    
    /**
     * 获取当前状态
     */
    private ProcessorState getCurrentState() {
        return ProcessorState.values()[processorState.get()];
    }
    
    /**
     * 设置状态
     */
    private boolean setState(ProcessorState newState) {
        ProcessorState oldState = getCurrentState();
        processorState.set(newState.ordinal());
        Log.d(TAG, "状态变化: " + oldState + " -> " + newState);
        return true;
    }
    
    /**
     * 检查状态是否允许操作
     */
    private boolean isStateAllowed(ProcessorState... allowedStates) {
        ProcessorState current = getCurrentState();
        for (ProcessorState allowed : allowedStates) {
            if (current == allowed) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 初始化处理器
     */
    public void initialize() {
        if (!isStateAllowed(ProcessorState.UNINITIALIZED)) {
            Log.w(TAG, "处理器状态不允许初始化，当前状态: " + getCurrentState());
            return;
        }
        
        setState(ProcessorState.INITIALIZING);
        Log.i(TAG, "开始初始化面部情感处理器");
        
        try {
            // 初始化ImageProcessHelper的RenderScript
            ImageProcessHelper.initialize(context);
            
            // 预热人脸模块对象池
            FaceMemoryMonitor.warmupFacePools();
            
            // 创建检测服务（不创建摄像头管理器）
            detectionService = new FaceDetectionService(context, this.emotionInterface);
            
            // 创建批次处理器
            batchProcessor = new FaceSimpleBatchProcessor(batchFrames);
            batchProcessor.setCallback(new FaceSimpleBatchProcessor.BatchProcessorCallback() {
                @Override
                public void onBatchComplete(List<FaceBox> faceBoxes, int batchIndex) {
                    // 将批次结果转换为数组格式用于回调
                    FaceBox[] batchArray = faceBoxes.toArray(new FaceBox[0]);
                    
                    if (callback != null) {
                        // 创建批次结果列表
                        java.util.List<FaceBox[]> batchResults = new java.util.ArrayList<>(1);
                        batchResults.add(batchArray);
                        
                        callback.onBatchComplete(batchResults, batchIndex);
                    }
                }
                
                @Override
                public void onProcessorError(String error) {
                    if (callback != null) {
                        callback.onError("批次处理器错误: " + error);
                    }
                }
                
                @Override
                public void onPerformanceUpdate(String performanceReport) {
                    Log.d(TAG, "批次处理器性能: " + performanceReport);
                }
            });
            
            // 初始化检测服务
            detectionService.initialize();
            
            setState(ProcessorState.INITIALIZED);
            Log.i(TAG, "面部情感处理器初始化完成");
            
            if (callback != null) {
                callback.onInitializationSuccess();
            }
            
        } catch (Exception e) {
            Log.e(TAG, "面部情感处理器初始化失败", e);
            setState(ProcessorState.ERROR);
            if (callback != null) {
                callback.onInitializationError(e.getMessage());
            }
        }
    }
    
    /**
     * 开始处理
     */
    public boolean startProcessing() {
        Log.i(TAG, "收到处理启动请求，当前状态: " + getCurrentState());
        
        if (!isStateAllowed(ProcessorState.INITIALIZED, ProcessorState.STOPPED)) {
            Log.w(TAG, "处理器状态不允许启动处理，当前状态: " + getCurrentState());
            return false;
        }
        
        setState(ProcessorState.RUNNING);
        Log.i(TAG, "开始面部检测处理");
        
        try {
            // 启动检测服务
            if (!detectionService.startDetection()) {
                Log.e(TAG, "检测服务启动失败");
                setState(ProcessorState.ERROR);
                return false;
            }
            
            isProcessing.set(true);
            Log.i(TAG, "面部检测处理已启动");
            
            if (callback != null) {
                callback.onProcessingStarted();
            }
            
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "启动面部检测处理失败", e);
            setState(ProcessorState.ERROR);
            if (callback != null) {
                callback.onError(e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * 处理单个图像帧
     * 这是核心方法，处理来自外部的图像数据
     * 
     * @param bitmap 要处理的图像帧
     */
    public void processFrame(Bitmap bitmap) {
        // 检查处理器状态
        if (!isStateAllowed(ProcessorState.RUNNING) || !isProcessing.get()) {
            Log.w(TAG, "处理器未在运行状态，当前状态: " + getCurrentState());
            return;
        }
        
        if (bitmap == null || detectionService == null) {
            Log.w(TAG, "输入图像为空或检测服务未初始化");
            return;
        }
        
        Log.d(TAG, "处理外部帧: " + bitmap.getWidth() + "x" + bitmap.getHeight());
        
        java.nio.ByteBuffer pooledBuffer = null;
        
        try {
            // 转换为ByteBuffer
            pooledBuffer = ImageProcessHelper.obtainBuffer();
            ImageProcessHelper.bitmapToBuffer(bitmap, pooledBuffer);
            
            // 调用优化版检测服务进行面部检测
            long detectionStart = System.nanoTime();
            java.nio.ByteBuffer outputRgbBuffer = null;
            int faceCount = 0;
            try {
                outputRgbBuffer = ImageProcessHelper.obtainBuffer();
                faceCount = detectionService.detectFacesOptimized(pooledBuffer, reusableResultList, outputRgbBuffer);
            } finally {
                if (outputRgbBuffer != null) {
                    ImageProcessHelper.recycleBuffer(outputRgbBuffer);
                }
            }
            long detectionEnd = System.nanoTime();
            
            // 记录性能统计
            totalFrames++;
            optimizedDetectionTime += (detectionEnd - detectionStart);
            
            // 处理检测结果
            List<FaceBox> resultsForCallback = null;
            if (faceCount > 0 && !reusableResultList.isEmpty()) {
                resultsForCallback = FaceBoxPool.obtainList();
                for (FaceBox originalFace : reusableResultList) {
                    FaceBox copyFace = FaceBoxPool.obtainFaceBox();
                    copyFace.copyFrom(originalFace);
                    resultsForCallback.add(copyFace);
                }
            }
            
            // 定期输出性能报告
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastPerformanceReport > 5000) {
                reportPerformanceStats();
                lastPerformanceReport = currentTime;
            }
            
            // 处理检测结果
            if (resultsForCallback != null && !resultsForCallback.isEmpty()) {
                // 回调单帧结果
                if (callback != null) {
                    callback.onFrameProcessed(resultsForCallback, (int)totalFrames);
                }
                
                // 提交给批次处理器
                for (FaceBox originalFace : reusableResultList) {
                    FaceBox faceForBatch = FaceBoxPool.obtainFaceBox();
                    faceForBatch.copyFrom(originalFace);
                    batchProcessor.processFaceBox(faceForBatch);
                }
                
            } else {
                // 没有检测到人脸
                if (callback != null) {
                    callback.onFrameProcessed(java.util.Collections.emptyList(), (int)totalFrames);
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "帧处理异常", e);
            if (callback != null) {
                callback.onError("帧处理异常: " + e.getMessage());
            }
        } finally {
            // 回收池化对象
            if (pooledBuffer != null) {
                ImageProcessHelper.recycleBuffer(pooledBuffer);
            }
            
            // 清理结果List
            if (reusableResultList != null) {
                FaceBoxPool.recycleFaceBoxList(reusableResultList);
                reusableResultList.clear();
            }
        }
    }

    /**
     * 停止处理
     */
    public void stopProcessing() {
        Log.i(TAG, "收到处理停止请求，当前状态: " + getCurrentState());

        if (!isStateAllowed(ProcessorState.RUNNING)) {
            Log.w(TAG, "当前状态不需要停止处理: " + getCurrentState());
            return;
        }

        setState(ProcessorState.STOPPED);
        Log.i(TAG, "停止面部检测处理");

        // 重置处理标志
        isProcessing.set(false);

        // 停止服务和批次处理器
        if (detectionService != null) {
            detectionService.stopDetection();
        }

        if (batchProcessor != null) {
            batchProcessor.forceCompleteBatch();
        }

        Log.i(TAG, "面部检测处理已停止");

        if (callback != null) {
            callback.onProcessingStopped();
        }
    }

    /**
     * 输出性能统计报告
     */
    private void reportPerformanceStats() {
        if (totalFrames > 0) {
            double avgDetectionTimeMs = (optimizedDetectionTime / totalFrames) / 1_000_000.0;
            double currentFps = totalFrames / 5.0;

            Log.i(TAG, "处理器性能统计:");
            Log.i(TAG, "  处理帧数: " + totalFrames);
            Log.i(TAG, "  平均检测时间: " + String.format("%.2f ms", avgDetectionTimeMs));
            Log.i(TAG, "  当前FPS: " + String.format("%.1f", currentFps));
            Log.i(TAG, "  List复用效率: " + reusableResultList.size() + " 个对象");
            Log.i(TAG, "  " + ImageProcessHelper.getPoolStats());

            // 向回调报告性能
            if (callback != null) {
                callback.onPerformanceUpdate(currentFps, avgDetectionTimeMs);
            }

            // 重置统计数据
            totalFrames = 0;
            optimizedDetectionTime = 0;
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        Log.i(TAG, "释放面部情感处理器资源");

        setState(ProcessorState.STOPPED);

        // 重置处理标志
        isProcessing.set(false);

        // 停止检测
        if (detectionService != null) {
            detectionService.stopDetection();
            detectionService.release();
        }

        // 释放批次处理器
        if (batchProcessor != null) {
            batchProcessor.release();
        }

        // 生成最终内存报告
        Log.i(TAG, FaceMemoryMonitor.generateMemoryReport());
        FaceMemoryMonitor.optimizeFacePools();

        // 释放JNI资源
        if (this.emotionInterface != null) {
            this.emotionInterface.releaseFaceModelSafe();
        }

        setState(ProcessorState.UNINITIALIZED);
        Log.i(TAG, "面部情感处理器资源释放完成");
    }

    /**
     * 获取处理状态
     */
    public boolean isProcessing() {
        return isProcessing.get() && isStateAllowed(ProcessorState.RUNNING);
    }

    /**
     * 获取当前状态字符串
     */
    public String getStateString() {
        return getCurrentState().toString();
    }

    /**
     * 面部处理器回调接口
     */
    public interface FaceProcessorCallback {
        /**
         * 初始化成功
         */
        void onInitializationSuccess();

        /**
         * 初始化错误
         */
        void onInitializationError(String error);

        /**
         * 处理已启动
         */
        void onProcessingStarted();

        /**
         * 处理已停止
         */
        void onProcessingStopped();

        /**
         * 帧处理完成
         * @param results 检测结果列表
         * @param frameIndex 帧索引
         */
        void onFrameProcessed(List<FaceBox> results, int frameIndex);

        /**
         * 批次处理完成
         * @param batchResults 批次结果列表
         * @param batchIndex 批次索引
         */
        void onBatchComplete(List<FaceBox[]> batchResults, int batchIndex);

        /**
         * 性能更新
         * @param fps 当前FPS
         * @param avgProcessTime 平均处理时间（毫秒）
         */
        void onPerformanceUpdate(double fps, double avgProcessTime);

        /**
         * 错误回调
         * @param error 错误信息
         */
        void onError(String error);
    }
}
