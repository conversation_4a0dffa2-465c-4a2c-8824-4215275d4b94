package com.emotion.face.sdk;

import android.content.Context;
import android.graphics.SurfaceTexture;
import android.media.ImageReader;
import android.util.Log;
import android.view.TextureView;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 面部情感检测模块
 * 负责协调面部检测和情感分析
 */
public class FaceEmotionModule implements ImageReader.OnImageAvailableListener {
    private static final String TAG = "FaceEmotionModule";
    
    // 镜像修正设置 - 统一对所有摄像头进行镜像修正
    private static final boolean FORCE_MIRROR_CORRECTION = true; // 强制镜像修正开关
    private static final boolean ENABLE_MIRROR_AUTO_DETECTION = false; // 禁用自动检测，统一处理
    
    // 状态枚举
    private enum ModuleState {
        UNINITIALIZED,
        INITIALIZING,
        INITIALIZED,
        STARTING,
        RUNNING,
        STOPPING,
        STOPPED,
        ERROR
    }
    
    private final Context context;
    private final FaceEmotionInterface emotionInterface;
    private final int batchFrames;
    
    private FaceDetectionService detectionService;
    private FaceDetectionView detectionView;
    private FaceCameraManager cameraManager;
    private FaceSimpleBatchProcessor batchProcessor;
    private FaceModuleCallback callback;
    
    private TextureView textureView;
    private SurfaceTexture surfaceTexture;
    
    // 可复用的检测结果List - 避免频繁创建
    private final List<FaceBox> reusableResultList = new ArrayList<>();
    
    // 性能统计
    private long totalFrames = 0;
    private long optimizedDetectionTime = 0;
    private long lastPerformanceReport = System.currentTimeMillis();
    
    // 使用原子状态管理
    private final AtomicInteger moduleState = new AtomicInteger(ModuleState.UNINITIALIZED.ordinal());
    private final AtomicBoolean pendingDetectionStart = new AtomicBoolean(false);
    private final AtomicBoolean surfaceReady = new AtomicBoolean(false);
    private final AtomicBoolean cameraSystemReady = new AtomicBoolean(false);

    // 外部帧模式支持
    private final AtomicBoolean externalFrameMode = new AtomicBoolean(false);
    
    /**
     * 构造函数
     */
    public FaceEmotionModule(Context context, int batchFrames) {
        this.context = context;
        this.batchFrames = batchFrames;
        this.emotionInterface = new FaceEmotionInterface();

        Log.i(TAG, "FaceEmotionModule创建完成，批次帧数: " + batchFrames);
    }
    
    /**
     * 设置回调
     */
    public void setCallback(FaceModuleCallback callback) {
        this.callback = callback;
    }
    
    /**
     * 设置显示视图
     */
    public void setDisplayView(FaceDetectionView view) {
        this.detectionView = view;
    }
    
    /**
     * 获取当前状态
     */
    private ModuleState getCurrentState() {
        return ModuleState.values()[moduleState.get()];
    }
    
    /**
     * 设置状态
     */
    private boolean setState(ModuleState newState) {
        ModuleState oldState = getCurrentState();
        moduleState.set(newState.ordinal());
        Log.d(TAG, "状态变化: " + oldState + " -> " + newState);
        return true;
    }
    
    /**
     * 检查状态是否允许操作
     */
    private boolean isStateAllowed(ModuleState... allowedStates) {
        ModuleState current = getCurrentState();
        for (ModuleState allowed : allowedStates) {
            if (current == allowed) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 初始化模块
     *
     * @param textureView 用于显示摄像头预览的TextureView，传入null则为纯外部帧模式
     */
    public void initialize(TextureView textureView) {
        // 允许从 UNINITIALIZED 或 ERROR 状态重新初始化
        if (!isStateAllowed(ModuleState.UNINITIALIZED, ModuleState.ERROR)) {
            Log.w(TAG, "模块状态不允许初始化，当前状态: " + getCurrentState());
            return;
        }

        setState(ModuleState.INITIALIZING);
        this.textureView = textureView;

        if (textureView == null) {
            Log.i(TAG, "开始初始化面部情感检测模块（纯外部帧模式）");
            initializeExternalFrameMode();
        } else {
            Log.i(TAG, "开始初始化面部情感检测模块（摄像头模式）");
            initializeCameraMode();
        }
    }

    /**
     * 初始化模块（外部摄像头管理模式）
     *
     * @param textureView 用于显示的TextureView
     * @param useExternalCamera 是否使用外部摄像头管理
     */
    public void initializeWithExternalCamera(TextureView textureView, boolean useExternalCamera) {
        // 允许从 UNINITIALIZED 或 ERROR 状态重新初始化
        if (!isStateAllowed(ModuleState.UNINITIALIZED, ModuleState.ERROR)) {
            Log.w(TAG, "模块状态不允许初始化，当前状态: " + getCurrentState());
            return;
        }

        setState(ModuleState.INITIALIZING);
        this.textureView = textureView;

        if (useExternalCamera) {
            Log.i(TAG, "开始初始化面部情感检测模块（外部摄像头管理模式）");
            initializeExternalFrameMode();  // 使用外部帧模式的初始化，但保留textureView用于显示
        } else {
            Log.i(TAG, "开始初始化面部情感检测模块（内部摄像头模式）");
            initializeCameraMode();
        }
    }

    /**
     * 初始化纯外部帧模式
     */
    private void initializeExternalFrameMode() {
        try {
            // 初始化ImageProcessHelper的RenderScript
            ImageProcessHelper.initialize(context);

            // 预热人脸模块对象池
            FaceMemoryMonitor.warmupFacePools();

            // 创建检测服务（不创建摄像头管理器）
            detectionService = new FaceDetectionService(context, this.emotionInterface);

            // 创建批次处理器
            createBatchProcessor();

            // 直接初始化检测服务
            detectionService.initialize();

            setState(ModuleState.INITIALIZED);
            Log.i(TAG, "面部情感检测模块初始化完成（纯外部帧模式）");

            if (callback != null) {
                callback.onInitializationSuccess();
            }

            // 检查是否有待启动的检测
            if (pendingDetectionStart.get()) {
                Log.i(TAG, "检测到延迟检测请求，自动启动检测");
                pendingDetectionStart.set(false);
                startDetectionInternal();
            }

        } catch (Exception e) {
            Log.e(TAG, "面部情感检测模块初始化失败（纯外部帧模式）", e);
            setState(ModuleState.ERROR);
            // 重置待启动标志，避免状态不一致
            pendingDetectionStart.set(false);
            if (callback != null) {
                callback.onInitializationError(e.getMessage());
            }
        }
    }

    /**
     * 初始化摄像头模式
     */
    private void initializeCameraMode() {
        try {
            // 初始化ImageProcessHelper的RenderScript
            ImageProcessHelper.initialize(context);

            // 预热人脸模块对象池
            FaceMemoryMonitor.warmupFacePools();

            // 创建服务组件
            detectionService = new FaceDetectionService(context, this.emotionInterface);
            cameraManager = new FaceCameraManager(context);

            // 创建批次处理器
            createBatchProcessor();
            
            // 设置摄像头回调
            cameraManager.setCallback(new FaceCameraManager.CameraCallback() {
                @Override
                public void onCameraOpened() {
                    Log.i(TAG, "摄像头已打开");
                    // 启动预览
                    if (cameraManager.startPreview()) {
                        Log.i(TAG, "摄像头预览启动请求已发送");
                    } else {
                        Log.e(TAG, "摄像头预览启动失败");
                        setState(ModuleState.ERROR);
                    }
                }
                
                @Override
                public void onCameraDisconnected() {
                    Log.w(TAG, "摄像头已断开");
                    cameraSystemReady.set(false);
                    if (callback != null) {
                        callback.onError("摄像头断开连接");
                    }
                }
                
                @Override
                public void onCameraError(int error) {
                    Log.e(TAG, "摄像头错误: " + error);
                    cameraSystemReady.set(false);
                    setState(ModuleState.ERROR);
                    if (callback != null) {
                        callback.onError("摄像头错误: " + error);
                    }
                }
                
                @Override
                public void onPreviewStarted() {
                    Log.i(TAG, "摄像头预览已就绪");
                    cameraSystemReady.set(true);
                    
                    // 如果模块还在初始化中，标记为已完成
                    if (getCurrentState() == ModuleState.INITIALIZING) {
                        setState(ModuleState.INITIALIZED);
                        if (callback != null) {
                            callback.onInitializationSuccess();
                        }
                    }
                    
                    // 检查是否有待启动的检测
                    if (pendingDetectionStart.get() && isStateAllowed(ModuleState.INITIALIZED)) {
                        Log.i(TAG, "检测到延迟检测请求，自动启动检测");
                        pendingDetectionStart.set(false);
                        scheduleDetectionStart(100);
                    }
                }
            });
            
            // 配置TextureView
            if (textureView != null) {
                textureView.setSurfaceTextureListener(new TextureView.SurfaceTextureListener() {
                    @Override
                    public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {
                        Log.i(TAG, "Surface可用: " + width + "x" + height);
                        surfaceTexture = surface;
                        surfaceReady.set(true);
                        
                        // 只有在需要时才初始化摄像头系统
                        if (getCurrentState() == ModuleState.INITIALIZING || 
                            (getCurrentState() == ModuleState.INITIALIZED && !cameraSystemReady.get())) {
                            initializeCameraSystem();
                        }
                    }
                    
                    @Override
                    public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {
                        Log.i(TAG, "Surface尺寸变化: " + width + "x" + height);
                    }
                    
                    @Override
                    public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {
                        Log.i(TAG, "Surface销毁");
                        surfaceReady.set(false);
                        cameraSystemReady.set(false);
                        if (cameraManager != null) {
                            cameraManager.closeCamera();
                        }
                        return true;
                    }
                    
                    @Override
                    public void onSurfaceTextureUpdated(SurfaceTexture surface) {
                        // 空实现 - 使用ImageReader处理
                    }
                });
            }
            
            Log.i(TAG, "面部情感检测模块组件初始化完成");
            
            // 如果Surface已经可用，立即初始化摄像头系统
            if (textureView != null && textureView.isAvailable()) {
                surfaceTexture = textureView.getSurfaceTexture();
                surfaceReady.set(true);
                initializeCameraSystem();
            } else {
                // 等待Surface可用
                Log.i(TAG, "等待Surface可用以完成初始化");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "面部情感检测模块初始化失败", e);
            setState(ModuleState.ERROR);
            if (callback != null) {
                callback.onInitializationError(e.getMessage());
            }
        }
    }
    
    /**
     * 初始化摄像头系统
     */
    private void initializeCameraSystem() {
        Log.i(TAG, "开始初始化摄像头系统");
        
        if (!surfaceReady.get() || surfaceTexture == null) {
            Log.w(TAG, "Surface未准备好，推迟摄像头系统初始化");
            return;
        }
        
        try {
            // 初始化摄像头管理器
            if (!cameraManager.initialize()) {
                Log.e(TAG, "摄像头管理器初始化失败");
                setState(ModuleState.ERROR);
                if (callback != null) {
                    callback.onInitializationError("摄像头管理器初始化失败");
                }
                return;
            }
            
            // 初始化检测服务
            detectionService.initialize();
            
            // 设置ImageReader
            android.util.Size previewSize = cameraManager.getPreviewSize();
            if (previewSize != null) {
                cameraManager.setupImageReader(previewSize.getWidth(), previewSize.getHeight(), this);
            }
            
            // 打开摄像头
            if (!cameraManager.openCamera(surfaceTexture)) {
                Log.e(TAG, "摄像头打开失败");
                setState(ModuleState.ERROR);
                if (callback != null) {
                    callback.onInitializationError("摄像头打开失败");
                }
                return;
            }
            
            Log.i(TAG, "摄像头系统初始化请求已发送");
            
        } catch (Exception e) {
            Log.e(TAG, "摄像头系统初始化失败", e);
            setState(ModuleState.ERROR);
            if (callback != null) {
                callback.onInitializationError("摄像头系统初始化失败: " + e.getMessage());
            }
        }
    }

    /**
     * 创建批次处理器
     */
    private void createBatchProcessor() {
        batchProcessor = new FaceSimpleBatchProcessor(batchFrames);
        batchProcessor.setCallback(new FaceSimpleBatchProcessor.BatchProcessorCallback() {
            @Override
            public void onBatchComplete(List<FaceBox> faceBoxes, int batchIndex) {
                // 将批次结果转换为数组格式用于回调
                FaceBox[] batchArray = faceBoxes.toArray(new FaceBox[0]);

                if (callback != null) {
                    // 创建批次结果列表
                    java.util.List<FaceBox[]> batchResults = new java.util.ArrayList<>(1);
                    batchResults.add(batchArray);

                    callback.onBatchComplete(batchResults, batchIndex);
                }
            }

            @Override
            public void onProcessorError(String error) {
                if (callback != null) {
                    callback.onError("批次处理器错误: " + error);
                }
            }

            @Override
            public void onPerformanceUpdate(String performanceReport) {
                Log.d(TAG, "批次处理器性能: " + performanceReport);
            }
        });
    }

    /**
     * 开始检测
     */
    public boolean startDetection() {
        Log.i(TAG, "收到检测启动请求，当前状态: " + getCurrentState());

        if (!isStateAllowed(ModuleState.INITIALIZED, ModuleState.STOPPED)) {
            // 如果正在初始化中，设置待启动标志
            if (getCurrentState() == ModuleState.INITIALIZING) {
                Log.i(TAG, "模块正在初始化中，设置延迟启动标志");
                pendingDetectionStart.set(true);
                return true;
            }
            Log.w(TAG, "模块状态不允许启动检测，当前状态: " + getCurrentState());
            return false;
        }

        // 如果是纯外部帧模式（没有摄像头管理器）
        if (cameraManager == null) {
            Log.i(TAG, "纯外部帧模式，直接启动检测服务");
            return startDetectionInternal();
        }

        // 摄像头模式的启动逻辑
        if (getCurrentState() == ModuleState.STOPPED) {
            // 从停止状态重新启动，需要重新初始化摄像头系统
            Log.i(TAG, "从停止状态重新启动，重新初始化摄像头系统");
            if (surfaceReady.get() && surfaceTexture != null) {
                initializeCameraSystem();
            }
            // 设置延迟启动标志
            pendingDetectionStart.set(true);
            return true;
        }

        // 检查系统是否就绪
        if (!cameraSystemReady.get()) {
            Log.w(TAG, "摄像头系统未就绪，设置延迟启动");
            pendingDetectionStart.set(true);
            return true;
        }

        return startDetectionInternal();
    }
    
    /**
     * 内部检测启动方法
     */
    private boolean startDetectionInternal() {
        if (!isStateAllowed(ModuleState.INITIALIZED, ModuleState.STOPPED)) {
            Log.w(TAG, "状态不允许启动检测: " + getCurrentState());
            return false;
        }
        
        setState(ModuleState.STARTING);
        Log.i(TAG, "开始面部检测");
        
        try {
            // 启动检测服务
            if (!detectionService.startDetection()) {
                Log.e(TAG, "检测服务启动失败");
                setState(ModuleState.ERROR);
                return false;
            }
            
            setState(ModuleState.RUNNING);
            Log.i(TAG, "面部检测已启动");
            
            if (callback != null) {
                callback.onDetectionStarted();
            }
            
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "启动面部检测失败", e);
            setState(ModuleState.ERROR);
            if (callback != null) {
                callback.onError(e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * 安全地安排检测启动
     */
    private void scheduleDetectionStart(long delayMs) {
        java.lang.ref.WeakReference<FaceEmotionModule> weakRef = new java.lang.ref.WeakReference<>(this);
        android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
        handler.postDelayed(() -> {
            FaceEmotionModule module = weakRef.get();
            if (module != null && module.cameraSystemReady.get()) {
                Log.i(TAG, "延迟启动检测执行");
                module.startDetectionInternal();
            }
        }, delayMs);
    }
    
    /**
     * 停止检测
     */
    public void stopDetection() {
        Log.i(TAG, "收到检测停止请求，当前状态: " + getCurrentState());
        
        if (!isStateAllowed(ModuleState.RUNNING, ModuleState.STARTING)) {
            Log.w(TAG, "当前状态不需要停止检测: " + getCurrentState());
            return;
        }
        
        setState(ModuleState.STOPPING);
        Log.i(TAG, "停止面部检测，开始释放资源");
        
        // 重置所有状态标志
        pendingDetectionStart.set(false);
        cameraSystemReady.set(false);
        
        // 停止服务和批次处理器
        if (detectionService != null) {
            detectionService.stopDetection();
        }
        
        if (batchProcessor != null) {
            batchProcessor.forceCompleteBatch();
        }
        
        // 关闭摄像头以完全释放硬件
        if (cameraManager != null) {
            cameraManager.closeCamera();
        }
        
        // 清除显示
        if (detectionView != null) {
            detectionView.clearFaces();
        }
        
        setState(ModuleState.STOPPED);
        Log.i(TAG, "面部检测已完全停止，资源已释放");
        
        if (callback != null) {
            callback.onDetectionStopped();
        }
    }
    
    /**
     * 暂停检测
     */
    public void pauseDetection() {
        if (!isStateAllowed(ModuleState.RUNNING)) {
            return;
        }
        
        Log.i(TAG, "暂停面部检测，释放摄像头");
        
        cameraSystemReady.set(false);
        
        if (cameraManager != null) {
            cameraManager.closeCamera();
        }
    }
    
    /**
     * 恢复检测
     */
    public void resumeDetection() {
        if (!isStateAllowed(ModuleState.RUNNING)) {
            return;
        }

        Log.i(TAG, "恢复面部检测，重新获取摄像头");

        // 检查SurfaceTexture是否依然可用
        if (this.textureView != null && this.textureView.isAvailable()) {
            this.surfaceTexture = this.textureView.getSurfaceTexture();
            surfaceReady.set(true);
            if (cameraManager != null) {
                cameraManager.openCamera(this.surfaceTexture);
            }
        } else {
            Log.w(TAG, "无法立即恢复，等待SurfaceTexture可用");
        }
    }

    /**
     * 切换到外部帧模式
     * 停止摄像头输入，允许通过processExternalFrame方法处理外部传入的视频帧
     */
    public void switchToExternalFrameMode() {
        Log.i(TAG, "切换到外部帧模式");

        // 设置外部帧模式标志
        externalFrameMode.set(true);

        // 关闭摄像头
        if (cameraManager != null) {
            Log.i(TAG, "关闭摄像头以切换到外部帧模式");
            cameraManager.closeCamera();
        }

        // 设置摄像头系统为未就绪状态
        cameraSystemReady.set(false);

        Log.i(TAG, "已切换到外部帧模式，摄像头已关闭");
    }

    /**
     * 切换回摄像头模式
     * 恢复摄像头输入，停止外部帧处理
     */
    public void switchToCameraMode() {
        Log.i(TAG, "切换回摄像头模式");

        // 设置外部帧模式标志为false
        externalFrameMode.set(false);

        // 恢复摄像头检测
        resumeDetection();

        Log.i(TAG, "已切换回摄像头模式");
    }

    /**
     * 处理外部传入的视频帧
     * 仅在外部帧模式下有效
     *
     * @param bitmap 外部传入的视频帧
     */
    public void processExternalFrame(android.graphics.Bitmap bitmap) {
        // 检查是否在外部帧模式
        if (!externalFrameMode.get()) {
            Log.w(TAG, "不在外部帧模式，忽略外部帧处理请求");
            return;
        }

        // 检查模块状态是否允许运行
        if (!isStateAllowed(ModuleState.RUNNING)) {
            Log.w(TAG, "模块状态不允许处理外部帧，当前状态: " + getCurrentState());
            return;
        }

        if (bitmap == null) {
            Log.w(TAG, "外部帧为空，跳过处理");
            return;
        }

        Log.d(TAG, "处理外部帧: " + bitmap.getWidth() + "x" + bitmap.getHeight());

        // 调用内部帧处理方法
        processFrameInternal(bitmap);
    }

    /**
     * 直接处理外部视频帧
     * 按照内部处理逻辑，保持所有内存管理和优化
     *
     * @param bitmap 外部视频帧数据
     */
    public void processFrame(android.graphics.Bitmap bitmap) {
        // 检查模块状态
        if (!isStateAllowed(ModuleState.RUNNING)) {
            Log.w(TAG, "模块未运行，忽略外部帧");
            return;
        }

        if (bitmap == null) {
            Log.w(TAG, "外部帧为空");
            return;
        }

        if (detectionService == null) {
            Log.w(TAG, "检测服务未初始化");
            return;
        }

        Log.v(TAG, "处理外部帧: " + bitmap.getWidth() + "x" + bitmap.getHeight());

        // 按照内部处理逻辑：定期执行内存监控
        if (totalFrames % 100 == 0) {
            FaceMemoryMonitor.performPeriodicCheck();
        }

        // 按照内部处理逻辑：直接处理（与onImageAvailable保持一致）
        try {
            // 直接调用内部帧处理方法（与onImageAvailable保持一致）
            processFrameInternal(bitmap);

        } catch (Exception e) {
            Log.e(TAG, "外部帧处理异常", e);
            if (callback != null) {
                callback.onError("外部帧处理异常: " + e.getMessage());
            }
        }
    }
    
    /**
     * 处理ImageReader的回调 - 仅用于内部摄像头模式
     *
     * 注意：在外部摄像头管理模式下，此方法不会被调用，因为：
     * 1. initializeExternalFrameMode() 不创建 cameraManager
     * 2. 没有 cameraManager 就没有 ImageReader
     * 3. 没有 ImageReader 就不会触发此回调
     *
     * 外部摄像头管理模式使用 processFrame(bitmap) 方法接收帧数据
     */
    @Override
    public void onImageAvailable(ImageReader reader) {
        if (!isStateAllowed(ModuleState.RUNNING) || !cameraSystemReady.get()) {
            return;
        }

        // 如果在外部帧模式，忽略摄像头帧（双重保险）
        if (externalFrameMode.get()) {
            Log.d(TAG, "在外部帧模式，忽略摄像头帧");
            return;
        }

        if (detectionService == null) {
            return;
        }

        android.media.Image image = null;
        try {
            image = reader.acquireLatestImage();
            if (image == null) {
                return;
            }

            // 定期执行人脸模块内存监控
            if (totalFrames % 100 == 0) {
                FaceMemoryMonitor.performPeriodicCheck();
            }

            // 获取池化对象
            android.graphics.Bitmap pooledBitmap = null;

            try {
                // YUV to ARGB Bitmap conversion
                pooledBitmap = ImageProcessHelper.obtainBitmap(image.getWidth(), image.getHeight());
                ImageProcessHelper.yuvToBitmap(image, pooledBitmap);

                // 调用内部帧处理方法
                processFrameInternal(pooledBitmap);

            } catch (Exception e) {
                Log.e(TAG, "后台线程处理异常", e);
                if (callback != null) {
                    callback.onError("帧处理异常: " + e.getMessage());
                }
            } finally {
                // 回收池化对象
                if (pooledBitmap != null) {
                    ImageProcessHelper.recycleBitmap(pooledBitmap);
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "ImageReader处理异常", e);
        } finally {
            if (image != null) {
                image.close();
            }
        }
    }

    /**
     * 内部帧处理方法
     * 复用现有的帧处理逻辑，支持摄像头帧和外部帧
     *
     * @param bitmap 要处理的帧数据
     */
    private void processFrameInternal(android.graphics.Bitmap bitmap) {
        if (bitmap == null || detectionService == null) {
            return;
        }

        java.nio.ByteBuffer pooledBuffer = null;

        try {
            // 转换为ByteBuffer
            pooledBuffer = ImageProcessHelper.obtainBuffer();
            ImageProcessHelper.bitmapToBuffer(bitmap, pooledBuffer);

            // 调用优化版检测服务进行面部检测
            long detectionStart = System.nanoTime();
            java.nio.ByteBuffer outputRgbBuffer = null;
            int faceCount = 0;
            try {
                outputRgbBuffer = ImageProcessHelper.obtainBuffer();
                faceCount = detectionService.detectFacesOptimized(pooledBuffer, reusableResultList, outputRgbBuffer);
            } finally {
                if (outputRgbBuffer != null) {
                    ImageProcessHelper.recycleBuffer(outputRgbBuffer);
                }
            }
            long detectionEnd = System.nanoTime();

            // 记录性能统计
            totalFrames++;
            optimizedDetectionTime += (detectionEnd - detectionStart);

            // 处理检测结果
            List<FaceBox> resultsForView = null;
            if (faceCount > 0 && !reusableResultList.isEmpty()) {
                resultsForView = FaceBoxPool.obtainList();
                for (FaceBox originalFace : reusableResultList) {
                    FaceBox copyFace = FaceBoxPool.obtainFaceBox();
                    copyFace.copyFrom(originalFace);
                    resultsForView.add(copyFace);
                }
            }

            // 定期输出性能报告
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastPerformanceReport > 5000) {
                reportPerformanceStats();
                lastPerformanceReport = currentTime;
            }

            // 处理检测结果
            if (resultsForView != null && !resultsForView.isEmpty()) {
                // 回调单帧结果
                if (callback != null) {
                    callback.onFrameProcessed(resultsForView, 0);
                }

                // 提交给批次处理器
                for (FaceBox originalFace : reusableResultList) {
                    FaceBox faceForBatch = FaceBoxPool.obtainFaceBox();
                    faceForBatch.copyFrom(originalFace);
                    batchProcessor.processFaceBox(faceForBatch);
                }

                // 更新显示视图 - 支持外部摄像头管理模式
                if (detectionView != null) {
                    int displayWidth, displayHeight;

                    // 优先使用TextureView尺寸（摄像头预览区域）
                    if (textureView != null) {
                        displayWidth = textureView.getWidth();
                        displayHeight = textureView.getHeight();
                        Log.d(TAG, "使用TextureView显示区域尺寸: " + displayWidth + "x" + displayHeight);
                    }
                    // 备用：使用FaceDetectionView尺寸
                    else {
                        displayWidth = detectionView.getWidth();
                        displayHeight = detectionView.getHeight();
                        Log.d(TAG, "使用FaceDetectionView显示区域尺寸: " + displayWidth + "x" + displayHeight);
                    }

                    // 确保显示区域有效
                    if (displayWidth > 0 && displayHeight > 0) {
                        // 使用精确的坐标转换
                        List<FaceBox> convertedResults = convertDetectionToDisplayCoordinates(
                            resultsForView, displayWidth, displayHeight);

                        // 转换后的坐标已经是最终坐标，不需要额外缩放
                        detectionView.setScale(1.0f, 1.0f);
                        detectionView.updateFaces(convertedResults);
                    } else {
                        Log.w(TAG, "显示区域尺寸无效，跳过人脸框显示");
                    }
                }

            } else {
                // 没有检测到人脸
                if (detectionView != null) {
                    detectionView.clearFaces();
                }

                if (callback != null) {
                    callback.onFrameProcessed(java.util.Collections.emptyList(), 0);
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "内部帧处理异常", e);
            if (callback != null) {
                callback.onError("内部帧处理异常: " + e.getMessage());
            }
        } finally {
            // 回收池化对象
            if (pooledBuffer != null) {
                ImageProcessHelper.recycleBuffer(pooledBuffer);
            }

            // 清理结果List
            if (reusableResultList != null) {
                FaceBoxPool.recycleFaceBoxList(reusableResultList);
                reusableResultList.clear();
            }
        }
    }
    

    
    /**
     * 输出性能统计报告
     */
    private void reportPerformanceStats() {
        if (totalFrames > 0) {
            double avgDetectionTimeMs = (optimizedDetectionTime / totalFrames) / 1_000_000.0;
            double currentFps = totalFrames / 5.0;
            
            Log.i(TAG, "优化版检测性能统计:");
            Log.i(TAG, "  处理帧数: " + totalFrames);
            Log.i(TAG, "  平均检测时间: " + String.format("%.2f ms", avgDetectionTimeMs));
            Log.i(TAG, "  当前FPS: " + String.format("%.1f", currentFps));
            Log.i(TAG, "  List复用效率: " + reusableResultList.size() + " 个对象");
            Log.i(TAG, "  " + ImageProcessHelper.getPoolStats());
            
            // 向回调报告性能
            if (callback != null) {
                callback.onPerformanceUpdate(currentFps, avgDetectionTimeMs);
            }
            
            // 重置统计数据
            totalFrames = 0;
            optimizedDetectionTime = 0;
        }
    }
    
    /**
     * 将Bitmap转换为byte数组 - 已弃用，使用ImageProcessHelper
     */
    @Deprecated
    private byte[] convertBitmapToByteArray(android.graphics.Bitmap bitmap) {
        try {
            int width = bitmap.getWidth();
            int height = bitmap.getHeight();
            int[] pixels = new int[width * height];
            bitmap.getPixels(pixels, 0, width, 0, 0, width, height);
            
            // 转换为RGB byte数组
            byte[] rgbBytes = new byte[width * height * 3];
            int index = 0;
            
            for (int pixel : pixels) {
                rgbBytes[index++] = (byte) ((pixel >> 16) & 0xFF); // R
                rgbBytes[index++] = (byte) ((pixel >> 8) & 0xFF);  // G
                rgbBytes[index++] = (byte) (pixel & 0xFF);         // B
            }
            
            return rgbBytes;
            
        } catch (Exception e) {
            Log.e(TAG, "转换Bitmap失败", e);
            return null;
        }

    }
    
    /**
     * 释放资源
     */
    public void release() {
        Log.i(TAG, "释放面部情感检测模块资源");
        
        setState(ModuleState.STOPPING);
        
        // 重置所有状态
        pendingDetectionStart.set(false);
        surfaceReady.set(false);
        cameraSystemReady.set(false);
        
        // 停止检测
        if (detectionService != null) {
            detectionService.stopDetection();
            detectionService.release();
        }
        
        // 关闭摄像头
        if (cameraManager != null) {
            cameraManager.release();
        }
        
        // 释放批次处理器
        if (batchProcessor != null) {
            batchProcessor.release();
        }
        
        // 清除显示
        if (detectionView != null) {
            detectionView.clearFaces();
        }
        
        // 生成最终内存报告
        Log.i(TAG, FaceMemoryMonitor.generateMemoryReport());
        FaceMemoryMonitor.optimizeFacePools();
        
        // 释放JNI资源
        if (this.emotionInterface != null) {
            this.emotionInterface.releaseFaceModelSafe();
        }
        
        setState(ModuleState.UNINITIALIZED);
        Log.i(TAG, "面部情感检测模块资源释放完成");
    }
    
    /**
     * 检查摄像头权限
     */
    private boolean checkCameraPermission() {
        return androidx.core.content.ContextCompat.checkSelfPermission(context, 
            android.Manifest.permission.CAMERA) == android.content.pm.PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * 面部模块回调接口
     */
    public interface FaceModuleCallback {
        void onInitializationSuccess();
        void onInitializationError(String error);
        void onDetectionStarted();
        void onDetectionStopped();
        void onFrameProcessed(List<FaceBox> results, int frameIndex);
        void onBatchComplete(List<FaceBox[]> batchResults, int batchIndex);
        void onPerformanceUpdate(double fps, double avgProcessTime);
        void onError(String error);
    }

    /**
     * 获取当前相机ID
     */
    private String getCurrentCameraId() {
        if (cameraManager != null) {
            return cameraManager.getCurrentCameraId();
        }
        return null;
    }

    /**
     * 获取摄像头输入分辨率 - 优化版本，确保准确性
     */
    private android.util.Size getCameraInputResolution() {
        Log.d(TAG, "开始获取摄像头分辨率");
        
        // 从camera manager获取实际分辨率
        if (cameraManager != null) {
            android.util.Size resolution = cameraManager.getCameraResolution();
            if (resolution != null) {
                Log.i(TAG, "成功获取实际摄像头分辨率: " + resolution.getWidth() + "x" + resolution.getHeight());
                return resolution;
            } else {
                Log.w(TAG, "摄像头管理器返回null分辨率");
                
                // 尝试获取缓存的分辨率
                android.util.Size lastKnown = cameraManager.getLastKnownResolution();
                if (lastKnown != null) {
                    Log.w(TAG, "使用最后已知的分辨率: " + lastKnown.getWidth() + "x" + lastKnown.getHeight());
                    return lastKnown;
                }
            }
        } else {
            Log.w(TAG, "摄像头管理器为null");
        }
        
        // 智能回退策略：优先使用1280x720
        Log.w(TAG, "无法从摄像头管理器获取分辨率，使用智能回退策略");
        android.util.Size smartDefault = new android.util.Size(1280, 720);
        
        Log.w(TAG, "使用智能默认分辨率: " + smartDefault.getWidth() + "x" + smartDefault.getHeight());
        Log.i(TAG, "建议检查摄像头初始化时序，确保previewSize在坐标转换前已设置");
        
        return smartDefault;
    }
    
    /**
     * 精确的坐标转换：检测坐标 → 显示坐标
     * 统一处理流程：摄像头分辨率 -> 640x640 -> 检测 -> 显示区域
     */
    private List<FaceBox> convertDetectionToDisplayCoordinates(List<FaceBox> detectionResults, 
                                                              int displayWidth, int displayHeight) {
        if (detectionResults == null || detectionResults.isEmpty()) {
            return detectionResults;
        }

        // 步骤1：获取摄像头输入尺寸
        android.util.Size inputSize = getCameraInputResolution();
        final int CAMERA_WIDTH = inputSize.getWidth();
        final int CAMERA_HEIGHT = inputSize.getHeight();
        final int MODEL_SIZE = 640;
        
        Log.d(TAG, "坐标转换开始 - 摄像头:" + CAMERA_WIDTH + "x" + CAMERA_HEIGHT + 
                   " 模型:" + MODEL_SIZE + "x" + MODEL_SIZE + 
                   " 显示:" + displayWidth + "x" + displayHeight);

        // 步骤2：计算摄像头图像在640x640模型中的缩放和偏移
        // 保持宽高比的缩放比例
        float scaleToModel = Math.min((float)MODEL_SIZE / CAMERA_WIDTH, (float)MODEL_SIZE / CAMERA_HEIGHT);
        
        // 缩放后的实际尺寸
        int scaledWidth = Math.round(CAMERA_WIDTH * scaleToModel);
        int scaledHeight = Math.round(CAMERA_HEIGHT * scaleToModel);
        
        // 在640x640画布中的居中偏移
        int offsetX = (MODEL_SIZE - scaledWidth) / 2;
        int offsetY = (MODEL_SIZE - scaledHeight) / 2;
        
        Log.d(TAG, "模型转换参数 - 缩放比例:" + String.format("%.4f", scaleToModel) + 
                   " 缩放后尺寸:" + scaledWidth + "x" + scaledHeight + 
                   " 偏移量:(" + offsetX + "," + offsetY + ")");

        // 步骤3：计算显示区域的缩放比例（保持宽高比）
        float scaleToDisplay = Math.min((float)displayWidth / CAMERA_WIDTH, (float)displayHeight / CAMERA_HEIGHT);
        
        // 显示区域中图像的实际尺寸（保持摄像头宽高比）
        int displayImageWidth = Math.round(CAMERA_WIDTH * scaleToDisplay);
        int displayImageHeight = Math.round(CAMERA_HEIGHT * scaleToDisplay);
        
        // 显示区域中的居中偏移（确保图像居中显示）
        int displayOffsetX = (displayWidth - displayImageWidth) / 2;
        int displayOffsetY = (displayHeight - displayImageHeight) / 2;
        
        Log.d(TAG, "显示转换参数 - 缩放比例:" + String.format("%.4f", scaleToDisplay) + 
                   " 显示图像尺寸:" + displayImageWidth + "x" + displayImageHeight + 
                   " 显示偏移:(" + displayOffsetX + "," + displayOffsetY + ")");

        // 步骤4：应用统一镜像修正（确保人脸框完美对齐）
        boolean needMirrorCorrection = shouldApplyMirrorCorrection();
        Log.d(TAG, "统一镜像修正: " + (needMirrorCorrection ? "启用" : "禁用"));

        // 步骤5：转换每个检测结果
        List<FaceBox> convertedResults = FaceBoxPool.obtainList();

        for (int i = 0; i < detectionResults.size(); i++) {
            FaceBox detection = detectionResults.get(i);
            
            Log.d(TAG, "转换人脸[" + i + "] - 原始检测坐标: (" + 
                   detection.x1 + "," + detection.y1 + "," + detection.x2 + "," + detection.y2 + ")");

            // 5.1：从640x640模型坐标转换回摄像头坐标
            // 减去填充偏移
            float modelX1 = detection.x1 - offsetX;
            float modelY1 = detection.y1 - offsetY;
            float modelX2 = detection.x2 - offsetX;
            float modelY2 = detection.y2 - offsetY;
            
            // 反向缩放到摄像头坐标
            float cameraX1 = modelX1 / scaleToModel;
            float cameraY1 = modelY1 / scaleToModel;
            float cameraX2 = modelX2 / scaleToModel;
            float cameraY2 = modelY2 / scaleToModel;
            
            Log.d(TAG, "转换人脸[" + i + "] - 摄像头坐标: (" + 
                   String.format("%.1f,%.1f,%.1f,%.1f", cameraX1, cameraY1, cameraX2, cameraY2) + ")");

            // 5.2：应用统一镜像修正（确保人脸框完美对齐）
            if (needMirrorCorrection) {
                float tempX1 = CAMERA_WIDTH - cameraX2;
                float tempX2 = CAMERA_WIDTH - cameraX1;
                cameraX1 = tempX1;
                cameraX2 = tempX2;
                Log.d(TAG, "转换人脸[" + i + "] - 统一镜像修正后: (" + 
                       String.format("%.1f,%.1f,%.1f,%.1f", cameraX1, cameraY1, cameraX2, cameraY2) + ")");
            } else {
                Log.d(TAG, "转换人脸[" + i + "] - 跳过镜像修正");
            }

            // 5.3：转换到显示区域坐标
            float displayX1 = cameraX1 * scaleToDisplay + displayOffsetX;
            float displayY1 = cameraY1 * scaleToDisplay + displayOffsetY;
            float displayX2 = cameraX2 * scaleToDisplay + displayOffsetX;
            float displayY2 = cameraY2 * scaleToDisplay + displayOffsetY;
            
            Log.d(TAG, "转换人脸[" + i + "] - 显示坐标: (" + 
                   String.format("%.1f,%.1f,%.1f,%.1f", displayX1, displayY1, displayX2, displayY2) + ")");

            // 5.4：边界检查和取整
            int finalX1 = Math.max(0, Math.min(Math.round(displayX1), displayWidth));
            int finalY1 = Math.max(0, Math.min(Math.round(displayY1), displayHeight));
            int finalX2 = Math.max(0, Math.min(Math.round(displayX2), displayWidth));
            int finalY2 = Math.max(0, Math.min(Math.round(displayY2), displayHeight));

            // 确保检测框有效
            if (finalX2 <= finalX1 || finalY2 <= finalY1) {
                Log.w(TAG, "转换人脸[" + i + "] - 无效检测框，跳过: (" + 
                       finalX1 + "," + finalY1 + "," + finalX2 + "," + finalY2 + ")");
                continue;
            }

            // 5.5：创建转换后的FaceBox
            FaceBox convertedFace = FaceBoxPool.obtainFaceBox();
            convertedFace.emotion = detection.emotion;
            convertedFace.x1 = finalX1;
            convertedFace.y1 = finalY1;
            convertedFace.x2 = finalX2;
            convertedFace.y2 = finalY2;
            convertedFace.score = detection.score;

            convertedResults.add(convertedFace);

            Log.i(TAG, "转换人脸[" + i + "] - 最终结果: (" + 
                   finalX1 + "," + finalY1 + "," + finalX2 + "," + finalY2 + 
                   ") 情感:" + detection.emotion + " 置信度:" + String.format("%.2f", detection.score));
        }

        Log.i(TAG, "坐标转换完成 - 成功转换" + convertedResults.size() + "个人脸框");
        return convertedResults;
    }

    /**
     * 判断是否需要应用镜像修正
     * 统一对所有摄像头进行镜像修正，确保人脸框完美对齐
     */
    private boolean shouldApplyMirrorCorrection() {
        Log.d(TAG, "统一镜像修正设置: " + FORCE_MIRROR_CORRECTION);
        return FORCE_MIRROR_CORRECTION;
    }
}
