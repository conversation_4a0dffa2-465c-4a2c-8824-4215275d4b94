package com.emotion.face.sdk;

import android.util.Log;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.List;
import java.util.ArrayList;

/**
 * 简化版人脸批次处理器
 * 专注于30帧批次处理和安全内存管理
 * 
 * 功能：
 * - 收集30个FaceBox对象形成批次
 * - 触发批次完成回调
 * - 使用标准Java集合进行内存管理
 * - 零内存泄漏设计
 */
public class FaceSimpleBatchProcessor {
    
    private static final String TAG = "FaceBatchProcessor";
    
    // 批次配置
    private static final int DEFAULT_BATCH_SIZE = 30; // 30fps，每秒一个批次
    
    // 批次处理状态
    private final int batchSize;
    private final List<FaceBox> currentBatch;
    private final AtomicInteger batchIndex = new AtomicInteger(0);
    
    // 统计信息
    private final AtomicLong totalFacesProcessed = new AtomicLong(0);
    private final AtomicLong totalBatchesCompleted = new AtomicLong(0);
    private final AtomicLong averageProcessingTime = new AtomicLong(0);
    private final AtomicLong lastBatchTime = new AtomicLong(0);
    
    // 回调接口
    private BatchProcessorCallback callback;
    
    /**
     * 批次处理器回调接口
     */
    public interface BatchProcessorCallback {
        /**
         * 批次完成回调
         * @param faceBoxes 30个FaceBox对象的批次。注意：此列表是临时的，
         * 在回调函数返回后将立即被回收，请勿在其他线程或之后使用它。
         * 如果需要长期持有数据，请在回调中自行拷贝。
         * @param batchIndex 批次索引
         */
        void onBatchComplete(List<FaceBox> faceBoxes, int batchIndex);
        
        /**
         * 处理器错误回调
         */
        void onProcessorError(String error);
        
        /**
         * 性能统计回调
         */
        void onPerformanceUpdate(String performanceReport);
    }
    
    /**
     * 构造函数
     * @param batchSize 批次大小（默认30）
     */
    public FaceSimpleBatchProcessor(int batchSize) {
        this.batchSize = Math.max(1, batchSize);
        this.currentBatch = FaceBoxPool.obtainList();
        
        Log.i(TAG, "批次处理器创建完成 - 使用FaceBox对象池");
    }
    
    /**
     * 构造函数（默认30帧批次）
     */
    public FaceSimpleBatchProcessor() {
        this(DEFAULT_BATCH_SIZE);
    }
    
    /**
     * 设置回调
     */
    public void setCallback(BatchProcessorCallback callback) {
        this.callback = callback;
    }
    
    /**
     * 处理单个FaceBox - 直接接受调用者提供的副本
     * @param faceBox 检测到的人脸对象，调用者保证这是一个可以被currentBatch持有的独立对象
     */
    public synchronized void processFaceBox(FaceBox faceBox) {
        if (faceBox == null) {
            return;
        }
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 直接添加调用者提供的FaceBox副本
            currentBatch.add(faceBox);
            totalFacesProcessed.incrementAndGet();
            
            if (Log.isLoggable(TAG, Log.DEBUG)) {
                Log.d(TAG, "收到FaceBox对象，直接添加到批次");
            }
            
            if (currentBatch.size() >= batchSize) {
                triggerBatchComplete();
            }
            
            long processingTime = System.currentTimeMillis() - startTime;
            updateProcessingTimeStats(processingTime);
            
        } catch (Exception e) {
            Log.e(TAG, "处理FaceBox异常", e);
            if (callback != null) {
                callback.onProcessorError("处理FaceBox异常: " + e.getMessage());
            }
        }
    }
    
    /**
     * 触发批次完成 - 防止内存泄漏版本
     */
    private void triggerBatchComplete() {
        // 使用对象池获取一个临时的批次列表
        List<FaceBox> batchResult = FaceBoxPool.obtainList();
        try {
            // 关键：这里不再是从池里拿新对象来拷贝，而是直接移动所有权。
            // 我们把 currentBatch 里的所有对象都"移动"到 batchResult 里。
            batchResult.addAll(currentBatch);
            currentBatch.clear(); // 清空当前批次，准备接收新数据
        
        int currentBatchIndex = batchIndex.incrementAndGet();
        totalBatchesCompleted.incrementAndGet();
        lastBatchTime.set(System.currentTimeMillis());
        
        if (Log.isLoggable(TAG, Log.DEBUG)) {
            Log.d(TAG, "批次处理完成");
        }
        
            // 触发回调，传递临时的 batchResult
        if (callback != null) {
            callback.onBatchComplete(batchResult, currentBatchIndex);
            }
        } finally {
            // 关键！无论回调函数做了什么，我们都在这里回收 batchResult 列表及其中的所有 FaceBox 对象。
            // 这就保证了内存绝对不会泄漏。
            FaceBoxPool.recycleList(batchResult);
        }
    }
    
    /**
     * 强制完成当前批次（即使未达到batchSize）
     */
    public synchronized void forceCompleteBatch() {
        if (!currentBatch.isEmpty()) {
            // 只在Debug模式下记录强制完成
            if (Log.isLoggable(TAG, Log.DEBUG)) {
                Log.d(TAG, "强制完成当前批次");
            }
            triggerBatchComplete();
        }
    }
    
    /**
     * 重置批次处理器 - 使用FaceBoxPool版本
     */
    public synchronized void reset() {
        // 回收当前批次中的所有对象
        FaceBoxPool.recycleFaceBoxList(currentBatch);
        
        // 重置统计
        batchIndex.set(0);
        totalFacesProcessed.set(0);
        totalBatchesCompleted.set(0);
        averageProcessingTime.set(0);
        lastBatchTime.set(System.currentTimeMillis());
        
        Log.i(TAG, "批次处理器已重置");
    }
    
    /**
     * 更新处理时间统计
     */
    private void updateProcessingTimeStats(long processingTime) {
        long currentAvg = averageProcessingTime.get();
        long totalFaces = totalFacesProcessed.get();
        
        if (totalFaces > 1) {
            // 计算滑动平均值
            long newAvg = (currentAvg * (totalFaces - 1) + processingTime) / totalFaces;
            averageProcessingTime.set(newAvg);
        } else {
            averageProcessingTime.set(processingTime);
        }
    }
    
    /**
     * 获取性能统计
     */
    public String getPerformanceStatistics() {
        long totalFaces = totalFacesProcessed.get();
        long totalBatches = totalBatchesCompleted.get();
        long avgTime = averageProcessingTime.get();
        
        return String.format(
            "性能统计: 处理人脸=%d个, 完成批次=%d个, 平均处理时间=%dms, 当前批次=%d/%d",
            totalFaces, totalBatches, avgTime, currentBatch.size(), batchSize
        );
    }
    
    /**
     * 获取批次频率（fps）
     */
    public double getBatchFrequency() {
        long currentTime = System.currentTimeMillis();
        long timeSinceLastBatch = currentTime - lastBatchTime.get();
        
        if (timeSinceLastBatch > 0 && totalBatchesCompleted.get() > 0) {
            return 1000.0 / timeSinceLastBatch; // 转换为每秒频率
        }
        return 0.0;
    }
    
    /**
     * 检查性能健康状态
     */
    public boolean checkPerformanceHealth() {
        double frequency = getBatchFrequency();
        long avgTime = averageProcessingTime.get();
        
        // 检查批次频率是否接近目标（1Hz，因为30帧/秒）
        boolean healthyFrequency = Math.abs(frequency - 1.0) < 0.2; // 允许20%误差
        
        // 检查平均处理时间是否合理（小于30ms）
        boolean healthyProcessingTime = avgTime < 30;
        
        boolean isHealthy = healthyFrequency && healthyProcessingTime;
        
        if (!isHealthy) {
            Log.w(TAG, "性能健康检查失败");
            Log.w(TAG, String.format("批次频率: %.2fHz (目标: 1.0Hz)", frequency));
            Log.w(TAG, String.format("平均处理时间: %dms (目标: <30ms)", avgTime));
        }
        
        return isHealthy;
    }
    
    /**
     * 获取详细状态
     */
    public String getDetailedStatus() {
        return String.format(
            "=== FaceSimpleBatchProcessor状态 ===\n" +
            "批次配置: %d帧/批次\n" +
            "当前进度: %d/%d\n" +
            "已完成批次: %d个\n" +
            "总处理人脸: %d个\n" +
            "批次频率: %.2fHz\n" +
            "平均处理时间: %dms\n" +
            "性能健康: %s\n" +
            "内存状态: FaceBoxPool管理\n" +
            "%s\n" +
            "=================================",
            batchSize, currentBatch.size(), batchSize,
            totalBatchesCompleted.get(),
            totalFacesProcessed.get(),
            getBatchFrequency(),
            averageProcessingTime.get(),
            checkPerformanceHealth() ? "健康" : "异常",
            FaceBoxPool.getStats()
        );
    }
    
    // ==================== 公共API ====================
    
    public int getBatchSize() {
        return batchSize;
    }
    
    public int getCurrentBatchSize() {
        return currentBatch.size();
    }
    
    public int getCompletedBatchCount() {
        return batchIndex.get();
    }
    
    public long getTotalFacesProcessed() {
        return totalFacesProcessed.get();
    }
    
    public boolean isReadyForBatch() {
        return currentBatch.size() >= batchSize;
    }
    
    public boolean isEmpty() {
        return currentBatch.isEmpty();
    }
    
    /**
     * 释放资源 - 使用FaceBoxPool版本
     */
    public void release() {
        Log.i(TAG, "释放批次处理器资源");
        
        // 强制完成当前批次
        forceCompleteBatch();
        
        // 回收批次列表及其中的对象
        FaceBoxPool.recycleList(currentBatch);
        
        // 输出最终统计
        Log.i(TAG, "最终统计: " + getPerformanceStatistics());
        Log.i(TAG, "对象池统计: " + FaceBoxPool.getStats());
        
        Log.i(TAG, "批次处理器资源释放完成");
    }
} 