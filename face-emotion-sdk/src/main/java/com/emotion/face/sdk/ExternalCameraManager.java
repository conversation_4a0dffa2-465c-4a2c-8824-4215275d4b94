package com.emotion.face.sdk;

import android.content.Context;
import android.graphics.Bitmap;
import android.media.Image;
import android.media.ImageReader;
import android.util.Log;
import android.util.Size;

/**
 * 外部摄像头管理器
 * 专门用于管理摄像头并将数据传递给 FaceEmotionProcessor
 * 
 * 设计理念：
 * - 完全分离摄像头管理和人脸检测
 * - 复用现有的 FaceCameraManager 功能
 * - 提供灵活的摄像头控制接口
 * - 将视频帧数据传递给外部，由外部决定如何处理
 */
public class ExternalCameraManager {
    private static final String TAG = "ExternalCameraManager";
    
    private final Context context;
    private FaceCameraManager cameraManager;
    private ExternalCameraCallback callback;
    
    // 摄像头状态
    private boolean isCameraReady = false;

    /**
     * 构造函数
     *
     * @param context 应用上下文
     */
    public ExternalCameraManager(Context context) {
        this.context = context;

        // 创建摄像头管理器
        this.cameraManager = new FaceCameraManager(context);

        Log.i(TAG, "ExternalCameraManager创建完成");
    }
    
    /**
     * 设置回调
     */
    public void setCallback(ExternalCameraCallback callback) {
        this.callback = callback;
    }
    
    /**
     * 获取摄像头管理器（用于高级控制）
     */
    public FaceCameraManager getCameraManager() {
        return cameraManager;
    }
    
    /**
     * 初始化
     */
    public void initialize() {
        Log.i(TAG, "开始初始化外部摄像头管理器");

        // 初始化摄像头管理器
        cameraManager.setCallback(new FaceCameraManager.CameraCallback() {
            @Override
            public void onCameraOpened() {
                Log.i(TAG, "摄像头已打开");
                if (callback != null) {
                    callback.onCameraOpened();
                }
            }

            @Override
            public void onPreviewStarted() {
                Log.i(TAG, "摄像头预览已启动");
                isCameraReady = true;

                if (callback != null) {
                    callback.onCameraReady();
                }
            }

            @Override
            public void onCameraDisconnected() {
                Log.w(TAG, "摄像头断开连接");
                isCameraReady = false;
                if (callback != null) {
                    callback.onCameraDisconnected();
                }
            }

            @Override
            public void onCameraError(int error) {
                Log.e(TAG, "摄像头错误: " + error);
                isCameraReady = false;
                if (callback != null) {
                    callback.onError("摄像头错误: " + error);
                }
            }
        });

        // 开始初始化
        if (cameraManager.initialize()) {
            setupImageReader();
        } else {
            Log.e(TAG, "摄像头管理器初始化失败");
            if (callback != null) {
                callback.onError("摄像头管理器初始化失败");
            }
        }
    }
    
    /**
     * 设置图像读取器
     */
    private void setupImageReader() {
        Size previewSize = cameraManager.getPreviewSize();
        if (previewSize != null) {
            Log.i(TAG, "设置ImageReader，分辨率: " + previewSize.getWidth() + "x" + previewSize.getHeight());

            cameraManager.setupImageReader(
                previewSize.getWidth(),
                previewSize.getHeight(),
                new ImageReader.OnImageAvailableListener() {
                    @Override
                    public void onImageAvailable(ImageReader reader) {
                        Image image = reader.acquireLatestImage();
                        if (image != null) {
                            try {
                                // 使用现有的 ImageProcessHelper 进行转换
                                Bitmap bitmap = ImageProcessHelper.obtainBitmap(
                                    image.getWidth(), image.getHeight());
                                ImageProcessHelper.yuvToBitmap(image, bitmap);

                                // 将帧数据传递给外部，由外部决定如何处理
                                if (callback != null) {
                                    callback.onFrameAvailable(bitmap);
                                }

                                // 回收资源
                                ImageProcessHelper.recycleBitmap(bitmap);

                            } catch (Exception e) {
                                Log.e(TAG, "图像处理失败", e);
                                if (callback != null) {
                                    callback.onError("图像处理失败: " + e.getMessage());
                                }
                            } finally {
                                image.close();
                            }
                        }
                    }
                }
            );
        } else {
            Log.e(TAG, "无法获取预览尺寸");
            if (callback != null) {
                callback.onError("无法获取摄像头预览尺寸");
            }
        }
    }
    
    /**
     * 启动摄像头
     */
    public boolean startCamera() {
        Log.i(TAG, "启动摄像头");

        // 打开摄像头（不需要 SurfaceTexture，因为只用于数据获取）
        cameraManager.openCamera(null);

        return true;
    }

    /**
     * 停止摄像头
     */
    public void stopCamera() {
        Log.i(TAG, "停止摄像头");

        // 关闭摄像头
        if (cameraManager != null) {
            cameraManager.closeCamera();
        }

        isCameraReady = false;
    }
    
    /**
     * 切换摄像头
     */
//    public void switchCamera() {
//        Log.i(TAG, "切换摄像头");
//        if (cameraManager != null) {
//            cameraManager.switchCamera();
//            // 重新设置 ImageReader
//            setupImageReader();
//        }
//    }
    
    /**
     * 获取当前摄像头ID
     */
    public String getCurrentCameraId() {
        return cameraManager != null ? cameraManager.getCurrentCameraId() : null;
    }
    
    /**
     * 释放资源
     */
    public void release() {
        Log.i(TAG, "释放外部摄像头管理器资源");

        // 停止摄像头
        stopCamera();

        // 释放摄像头管理器
        if (cameraManager != null) {
            cameraManager.release();
        }

        isCameraReady = false;

        Log.i(TAG, "外部摄像头管理器资源释放完成");
    }

    /**
     * 获取状态信息
     */
    public String getStatusInfo() {
        return "摄像头就绪: " + isCameraReady;
    }

    /**
     * 外部摄像头管理器回调接口
     */
    public interface ExternalCameraCallback {
        /**
         * 摄像头已打开
         */
        void onCameraOpened();

        /**
         * 摄像头就绪
         */
        void onCameraReady();

        /**
         * 摄像头断开连接
         */
        void onCameraDisconnected();

        /**
         * 帧数据可用（核心回调）
         * 外部可以获取到摄像头的每一帧数据，自行决定如何处理
         *
         * @param bitmap 摄像头帧数据
         */
        void onFrameAvailable(Bitmap bitmap);

        /**
         * 错误回调
         * @param error 错误信息
         */
        void onError(String error);
    }
}
