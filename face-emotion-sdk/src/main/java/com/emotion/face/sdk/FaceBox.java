package com.emotion.face.sdk;

import android.graphics.PointF;
import android.graphics.RectF;

public class FaceBox {
    public String emotion;
    public int x1, y1, x2, y2;
    public float score;
    
    // 兼容旧版本的字段
    @Deprecated
    public float confidence;
    @Deprecated
    public float emotionConfidence;
    
    // 默认构造函数 - 用于对象池
    public FaceBox() {
        this.emotion = "";
        this.x1 = 0;
        this.y1 = 0;
        this.x2 = 0;
        this.y2 = 0;
        this.score = 0.0f;
        this.confidence = 0.0f;
        this.emotionConfidence = 0.0f;
    }
    
    // 主构造函数
    public FaceBox(String emotion, int x1, int y1, int x2, int y2, float score) {
        this.emotion = emotion;
        this.x1 = x1;
        this.y1 = y1;
        this.x2 = x2;
        this.y2 = y2;
        this.score = score;
        // 为了向后兼容，设置旧字段
        this.confidence = score;
        this.emotionConfidence = score;
    }
    
    // 拷贝构造函数
    public FaceBox(FaceBox other) {
        if (other != null) {
            this.emotion = other.emotion;
            this.x1 = other.x1;
            this.y1 = other.y1;
            this.x2 = other.x2;
            this.y2 = other.y2;
            this.score = other.score;
            this.confidence = other.confidence;
            this.emotionConfidence = other.emotionConfidence;
        }
    }
    
    // 兼容旧版本的构造函数
    @Deprecated
    public FaceBox(String emotion, float x, float y,
                   float width, float height, float confidence) {
        this.emotion = emotion;
        this.x1 = Math.round(x);
        this.y1 = Math.round(y);
        this.x2 = Math.round(x + width);
        this.y2 = Math.round(y + height);
        this.score = confidence;
        // 为了向后兼容，设置旧字段
        this.confidence = confidence;
        this.emotionConfidence = confidence;
    }
    
    // 兼容旧版本的getter方法
    @Deprecated
    public float getX() { return x1; }
    
    @Deprecated
    public float getY() { return y1; }
    
    @Deprecated
    public float getConfidence() { return score; }
    
    // 新的getter方法
    public String getEmotion() { return emotion; }
    public int getX1() { return x1; }
    public int getY1() { return y1; }
    public int getX2() { return x2; }
    public int getY2() { return y2; }
    public float getScore() { return score; }
    
    // 获取宽度
    public int getWidth() {
        return x2 - x1;
    }
    
    // 获取高度
    public int getHeight() {
        return y2 - y1;
    }
    
    // 获取Android RectF对象
    public RectF getBoundingRect() {
        return new RectF(x1, y1, x2, y2);
    }
    
    // 获取中心点
    public PointF getCenterPoint() {
        return new PointF((x1 + x2) / 2f, (y1 + y2) / 2f);
    }
    
    // 获取面积
    public int getArea() {
        return getWidth() * getHeight();
    }
    
    // 对象池支持方法
    
    /**
     * 复制另一个FaceBox对象的数据
     */
    public void copyFrom(FaceBox other) {
        if (other != null) {
            this.emotion = other.emotion;
            this.x1 = other.x1;
            this.y1 = other.y1;
            this.x2 = other.x2;
            this.y2 = other.y2;
            this.score = other.score;
            this.confidence = other.confidence;
            this.emotionConfidence = other.emotionConfidence;
        }
    }
    
    /**
     * 重置对象状态 - 用于对象池回收
     */
    public void reset() {
        this.emotion = "";
        this.x1 = 0;
        this.y1 = 0;
        this.x2 = 0;
        this.y2 = 0;
        this.score = 0.0f;
        this.confidence = 0.0f;
        this.emotionConfidence = 0.0f;
    }
    
    // 坐标转换：模型坐标转显示坐标
    public static FaceBox convertToDisplayCoordinates(
        FaceBox modelResult,
        float displayWidth, float displayHeight,
        float modelWidth, float modelHeight) {
        
        float scaleX = displayWidth / modelWidth;
        float scaleY = displayHeight / modelHeight;
        
        int newX1 = Math.round(modelResult.x1 * scaleX);
        int newY1 = Math.round(modelResult.y1 * scaleY);
        int newX2 = Math.round(modelResult.x2 * scaleX);
        int newY2 = Math.round(modelResult.y2 * scaleY);
        
        return new FaceBox(
            modelResult.emotion,
            newX1, newY1, newX2, newY2,
            modelResult.score
        );
    }
    
    @Override
    public String toString() {
        return String.format("FaceBox{emotion='%s', x1=%d, y1=%d, x2=%d, y2=%d, score=%.2f}", 
                           emotion, x1, y1, x2, y2, score);
    }
} 