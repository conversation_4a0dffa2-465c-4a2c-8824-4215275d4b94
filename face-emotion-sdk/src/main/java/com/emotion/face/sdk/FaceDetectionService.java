package com.emotion.face.sdk;

import android.content.Context;
import android.util.Log;
import java.nio.ByteBuffer;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.io.InputStream;
import java.io.FileOutputStream;
import java.io.File;

/**
 * 面部检测服务
 * 负责面部检测的具体实现
 */
public class FaceDetectionService {
    private static final String TAG = "FaceDetectionService";

    private final Context context;
    private final AtomicBoolean isInitialized = new AtomicBoolean(false);
    private final AtomicBoolean isDetecting = new AtomicBoolean(false);

    private FaceEmotionInterface faceInterface;

    /**
     * 构造函数
     */
    public FaceDetectionService(Context context, FaceEmotionInterface faceInterface) {
        this.context = context;
        this.faceInterface = faceInterface; // 使用外部传入的实例
        Log.i(TAG, "FaceDetectionService 创建");
    }

    /**
     * 初始化服务
     */
    public void initialize() {
        if (isInitialized.get()) {
            Log.w(TAG, "服务已经初始化");
            return;
        }

        Log.i(TAG, "开始初始化面部检测服务");

        try {
            // 初始化模型
            initializeModels();

            isInitialized.set(true);
            Log.i(TAG, "✅ 面部检测服务初始化成功");

        } catch (Exception e) {
            Log.e(TAG, "面部检测服务初始化失败: " + e.getMessage());
            // 关键修复：确保失败时状态为false，这样下次可以重新尝试
            isInitialized.set(false);
            Log.i(TAG, "继续运行，但面部检测功能将不可用");
            // 不抛异常，让程序继续运行
        }
    }

    /**
     * 初始化模型
     */
    private void initializeModels() {
        try {
            Log.i(TAG, "开始初始化面部检测模型...");
            
            // 第1步：复制模型文件到内部存储
            Log.i(TAG, "1. 复制模型文件到内部存储...");
            String faceDetectionModelPath = copyModelToInternalStorage(
                "models/yolo_free_tiny_widerface_640x640_rk3588.rknn", 
                "face_model.rknn"
            );
            String faceEmotionModelPath = copyModelToInternalStorage(
                "models/mobilenet_v2_emotion_static_sim.rknn", 
                "emotion_model.rknn"
            );

            if (faceDetectionModelPath == null || faceEmotionModelPath == null) {
                Log.e(TAG, "模型文件复制失败");
                throw new RuntimeException("关键模型加载失败，服务无法初始化");
            }

            Log.i(TAG, "✅ 面部检测模型路径: " + faceDetectionModelPath);
            Log.i(TAG, "✅ 情感识别模型路径: " + faceEmotionModelPath);
            
            // 第2步：初始化模型
            Log.i(TAG, "2. 初始化RKNN模型...");
            boolean result = faceInterface.initModel(faceDetectionModelPath, faceEmotionModelPath);

            if (result) {
                Log.i(TAG, "✅ 面部检测模型加载成功");
            } else {
                Log.e(TAG, "❌ 面部检测模型加载失败，可能是设备不支持或驱动问题");
                throw new RuntimeException("关键模型加载失败，服务无法初始化");
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ 面部检测模型加载异常: " + e.getMessage());
            Log.e(TAG, "详细异常信息", e);
            Log.i(TAG, "继续运行，但面部检测功能将不可用");
            // 重新抛出异常，让上层知道初始化失败
            throw e;
        }
    }
    
    /**
     * 检查模型文件是否存在
     */
    private boolean checkModelFile(String filePath) {
        try {
            Log.d(TAG, "验证模型文件: " + filePath);
            
            if (filePath.startsWith("file:///android_asset/")) {
                // Assets路径 - 使用AssetManager验证
                String filename = filePath.substring("file:///android_asset/".length());
                Log.d(TAG, "验证assets文件: " + filename);
                context.getAssets().open(filename).close();
                Log.d(TAG, "✅ Assets模型文件存在: " + filename);
                return true;
            } else if (filePath.startsWith("assets/")) {
                // 相对assets路径 - 使用AssetManager验证
                Log.d(TAG, "验证assets文件: " + filePath);
                context.getAssets().open(filePath).close();
                Log.d(TAG, "✅ Assets模型文件存在: " + filePath);
                return true;
            } else {
                // 内部存储路径 - 使用File验证
                Log.d(TAG, "验证内部存储文件: " + filePath);
                java.io.File file = new java.io.File(filePath);
                boolean exists = file.exists();
                boolean canRead = file.canRead();
                long size = file.length();
                
                Log.d(TAG, String.format("文件状态: 存在=%b, 可读=%b, 大小=%d字节", exists, canRead, size));
                
                if (exists && canRead && size > 0) {
                    Log.d(TAG, "✅ 内部存储模型文件验证通过: " + filePath);
                    return true;
                } else {
                    Log.e(TAG, "❌ 内部存储模型文件验证失败: " + filePath);
                    return false;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ 模型文件不存在或无法访问: " + filePath);
            Log.e(TAG, "文件验证详细错误", e);
            return false;
        }
    }

    /**
     * 将模型文件从assets复制到内部存储
     */
    private String copyModelToInternalStorage(String assetPath, String fileName) {
        try {
            Log.i(TAG, "复制模型文件: " + assetPath + " -> " + fileName);
            
            // 检查文件是否已存在
            File outputFile = new File(context.getFilesDir(), fileName);
            if (outputFile.exists()) {
                Log.i(TAG, "文件已存在，跳过复制: " + outputFile.getAbsolutePath());
                return outputFile.getAbsolutePath();
            }
            
            InputStream inputStream = context.getAssets().open(assetPath);
            FileOutputStream outputStream = new FileOutputStream(outputFile);
            
            byte[] buffer = new byte[8192];
            int length;
            long totalBytes = 0;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
                totalBytes += length;
            }
            
            inputStream.close();
            outputStream.close();
            
            Log.i(TAG, "✅ 模型文件复制成功: " + outputFile.getAbsolutePath() + " (大小: " + totalBytes + " 字节)");
            return outputFile.getAbsolutePath();
            
        } catch (Exception e) {
            Log.e(TAG, "❌ 模型文件复制失败: " + assetPath, e);
            return null;
        }
    }

    /**
     * 开始检测
     */
    public boolean startDetection() {
        if (!isInitialized.get()) {
            Log.w(TAG, "服务未初始化");
            return false;
        }

        if (isDetecting.get()) {
            Log.w(TAG, "检测已在进行中");
            return true;
        }

        Log.i(TAG, "开始面部检测");

        try {
            // 简单启动检测（不依赖摄像头）
            isDetecting.set(true);
            Log.i(TAG, "✅ 面部检测服务已启动");
            return true;

        } catch (Exception e) {
            Log.e(TAG, "❌ 启动面部检测失败", e);
            return false;
        }
    }

    /**
     * 停止检测
     */
    public void stopDetection() {
        if (!isDetecting.get()) {
            Log.w(TAG, "检测未在进行中");
            return;
        }

        Log.i(TAG, "停止面部检测");

        try {
            // 简单停止检测
            isDetecting.set(false);
            Log.i(TAG, "✅ 面部检测服务已停止");

        } catch (Exception e) {
            Log.e(TAG, "停止面部检测时出错", e);
        }
    }

    /**
     * 检测面部 - 接受ByteBuffer（新版本）
     */
    public FaceBox[] detectFaces(ByteBuffer imageBuffer, ByteBuffer outputRgbBuffer) {
        if (!isInitialized.get()) {
            Log.w(TAG, "服务未初始化");
            return new FaceBox[0];
        }

        if (imageBuffer == null) {
            Log.w(TAG, "图像数据为空");
            return new FaceBox[0];
        }

        try {
            // 直接调用JNI进行面部检测
            FaceBox[] results = faceInterface.detectFace(imageBuffer, outputRgbBuffer);
            return results != null ? results : new FaceBox[0];

        } catch (Exception e) {
            Log.e(TAG, "面部检测时出错", e);
            return new FaceBox[0];
        }
    }
    
    /**
     * 优化版面部检测 - 直接填充到List中，避免数组创建开销
     * @param imageBuffer ARGB格式的图像数据ByteBuffer
     * @param resultList 用于存储检测结果的List，会被清空后填入新结果
     * @param outputRgbBuffer 用于C++层RGB数据处理的ByteBuffer
     * @return 检测到的人脸数量，-1表示出错
     */
    public int detectFacesOptimized(ByteBuffer imageBuffer, List<FaceBox> resultList, ByteBuffer outputRgbBuffer) {
        if (!isInitialized.get()) {
            Log.w(TAG, "服务未初始化");
            return -1;
        }

        if (imageBuffer == null) {
            Log.w(TAG, "图像数据为空");
            return -1;
        }
        
        if (resultList == null) {
            Log.w(TAG, "结果List为空");
            return -1;
        }

        try {
            // 验证ByteBuffer大小
            int expectedSize = 640 * 640 * 3; // RGB格式
            if (imageBuffer.capacity() != expectedSize) {
                Log.w(TAG, "ByteBuffer大小不匹配: 实际=" + imageBuffer.capacity() + ", 期望=" + expectedSize);
            } else {
                Log.d(TAG, "ByteBuffer大小验证通过: " + imageBuffer.capacity() + " 字节 (640x640x3 RGB)");
            }
            
            // 调用优化版JNI进行面部检测 - 直接操作List
            int faceCount = faceInterface.detectFaceOptimized(imageBuffer, resultList, outputRgbBuffer);
            
            if (faceCount >= 0) {
                Log.d(TAG, "✅ 优化版检测完成，检测到 " + faceCount + " 个人脸，List大小: " + resultList.size());
            } else {
                Log.w(TAG, "⚠️ 优化版检测失败，返回: " + faceCount);
            }
            
            return faceCount;

        } catch (Exception e) {
            Log.e(TAG, "优化版面部检测时出错", e);
            return -1;
        }
    }

    /**
     * 检测面部 - 接受byte数组（旧版本兼容）
     */
    public FaceBox[] detectFaces(byte[] imageData, int width, int height) {
        if (!isInitialized.get()) {
            Log.w(TAG, "服务未初始化");
            return new FaceBox[0];
        }

        try {
            // 将byte[]转换为ByteBuffer（JNI需要Direct ByteBuffer）
            ByteBuffer imageBuffer = convertToDirectBuffer(imageData, width, height);

            if (imageBuffer == null) {
                Log.w(TAG, "图像数据转换失败");
                return new FaceBox[0];
            }

            // 获取输出RGB Buffer
            ByteBuffer outputRgbBuffer = ImageProcessHelper.obtainBuffer();
            FaceBox[] results = null;
            
            try {
                // 调用ByteBuffer版本的detectFaces
                results = detectFaces(imageBuffer, outputRgbBuffer);
            } finally {
                // 回收outputRgbBuffer
                if (outputRgbBuffer != null) {
                    ImageProcessHelper.recycleBuffer(outputRgbBuffer);
                }
            }

            // 回收ByteBuffer
            if (imageBuffer.isDirect()) {
                ImageProcessHelper.recycleBuffer(imageBuffer);
            }

            return results;

        } catch (Exception e) {
            Log.e(TAG, "面部检测时出错", e);
            return new FaceBox[0];
        }
    }

    /**
     * 将byte数组转换为Direct ByteBuffer
     */
    private ByteBuffer convertToDirectBuffer(byte[] imageData, int width, int height) {
        try {
            // 如果输入不是640x640，需要调整大小
            if (width != 640 || height != 640) {
                Log.d(TAG, "调整图像大小从 " + width + "x" + height + " 到 640x640");
                // 这里应该实现图像缩放逻辑
            }

            // 获取Direct ByteBuffer
            ByteBuffer buffer = ImageProcessHelper.obtainBuffer();

            // 将数据放入buffer
            if (imageData.length >= 640 * 640 * 3) {
                buffer.put(imageData, 0, 640 * 640 * 3);
            } else {
                // 数据不足，填充零
                buffer.put(imageData);
                while (buffer.remaining() > 0) {
                    buffer.put((byte) 0);
                }
            }

            buffer.flip();
            return buffer;

        } catch (Exception e) {
            Log.e(TAG, "转换ByteBuffer失败", e);
            return null;
        }
    }

    /**
     * 获取检测状态
     */
    public boolean isDetecting() {
        return isDetecting.get();
    }

    /**
     * 获取初始化状态
     */
    public boolean isInitialized() {
        return isInitialized.get();
    }

    /**
     * 释放资源
     */
    public void release() {
        Log.i(TAG, "释放面部检测服务资源");

        stopDetection();

        // 释放JNI资源
        if (faceInterface != null) {
            try {
                faceInterface.releaseFaceModel();
                Log.i(TAG, "✅ JNI模型资源释放成功");
            } catch (Exception e) {
                Log.e(TAG, "❌ JNI模型资源释放失败", e);
            }
        }

        isInitialized.set(false);

        Log.i(TAG, "✅ 面部检测服务资源释放完成");
    }
} 
