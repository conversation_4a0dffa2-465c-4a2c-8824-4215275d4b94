package com.emotion.face.sdk;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.SurfaceTexture;
import android.hardware.camera2.CameraAccessException;
import android.hardware.camera2.CameraCaptureSession;
import android.hardware.camera2.CameraCharacteristics;
import android.hardware.camera2.CameraDevice;
import android.hardware.camera2.CameraManager;
import android.hardware.camera2.CaptureRequest;
import android.hardware.camera2.params.StreamConfigurationMap;
import android.media.ImageReader;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.util.Size;
import android.view.Surface;
import androidx.core.content.ContextCompat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * 摄像头管理器
 * 负责摄像头的初始化、预览管理和资源释放
 */
public class FaceCameraManager {
    
    private static final String TAG = "FaceCameraManager";
    private static final int MAX_PREVIEW_WIDTH = 1920;
    private static final int MAX_PREVIEW_HEIGHT = 1080;
    
    // rk3588外置摄像头特殊配置
    private static final int RK3588_CAMERA_OPEN_TIMEOUT = 5000; // 5秒超时
    private static final int RK3588_EXTERNAL_CAMERA_MIN_WIDTH = 1280; // 外置摄像头最小分辨率识别
    
    // Core components
    private final Context context;
    private CameraManager cameraManager;
    private String cameraId;
    private Size previewSize;
    private android.util.Size lastKnownResolution; // 缓存最后已知的分辨率
    
    // Camera state
    private CameraDevice cameraDevice;
    private CameraCaptureSession captureSession;
    private CaptureRequest.Builder previewRequestBuilder;
    private CaptureRequest previewRequest;
    
    // Threading
    private HandlerThread backgroundThread;
    private Handler backgroundHandler;
    private final Semaphore cameraOpenCloseLock = new Semaphore(1);
    
    // Surface
    private Surface previewSurface;
    private SurfaceTexture surfaceTexture;
    
    // ImageReader for background processing
    private ImageReader imageReader;
    private HandlerThread imageReaderThread;
    private Handler imageReaderHandler;
    
    // Callbacks
    private CameraCallback callback;
    
    public interface CameraCallback {
        void onCameraOpened();
        void onCameraDisconnected();
        void onCameraError(int error);
        void onPreviewStarted();
    }
    
    private final CameraDevice.StateCallback stateCallback = new CameraDevice.StateCallback() {
        @Override
        public void onOpened(CameraDevice camera) {
            cameraOpenCloseLock.release();
            cameraDevice = camera;
            Log.i(TAG, "✅ rk3588摄像头成功打开: " + cameraId);
            if (callback != null) {
                callback.onCameraOpened();
            }
        }
        
        @Override
        public void onDisconnected(CameraDevice camera) {
            cameraOpenCloseLock.release();
            closeCamera();
            Log.w(TAG, "⚠️ rk3588摄像头断开连接: " + cameraId);
            if (callback != null) {
                callback.onCameraDisconnected();
            }
        }
        
        @Override
        public void onError(CameraDevice camera, int error) {
            cameraOpenCloseLock.release();
            closeCamera();
            String errorMsg = getCameraErrorMessage(error);
            Log.e(TAG, "× rk3588摄像头错误: " + errorMsg + " (错误码: " + error + ")");
            if (callback != null) {
                callback.onCameraError(error);
            }
        }
    };
    
    public FaceCameraManager(Context context) {
        this.context = context.getApplicationContext();
        this.cameraManager = (CameraManager) context.getSystemService(Context.CAMERA_SERVICE);
    }
    
    public void setCallback(CameraCallback callback) {
        this.callback = callback;
    }
    
    /**
     * 获取摄像头错误消息（兼容不同API级别）
     */
    private String getCameraErrorMessage(int error) {
        // 使用数值常量，避免API级别兼容问题
        switch (error) {
            case 1: // STATE_ERROR_CAMERA_DEVICE
                return "摄像头设备错误";
            case 2: // STATE_ERROR_CAMERA_DISABLED  
                return "摄像头被禁用";
            case 3: // STATE_ERROR_CAMERA_IN_USE
                return "摄像头正在使用中";
            case 4: // STATE_ERROR_CAMERA_SERVICE
                return "摄像头服务错误";
            case 5: // STATE_ERROR_MAX_CAMERAS_IN_USE
                return "已达到最大摄像头使用数量";
            default:
                return "未知错误";
        }
    }
    
    /**
     * 初始化摄像头
     */
    public boolean initialize() {
        try {
            setupCameraId();
            setupPreviewSize();
            return true;
        } catch (Exception e) {
            Log.e(TAG, "× Camera initialization failed", e);
            return false;
        }
    }
    
    private void setupCameraId() throws CameraAccessException {
        Log.i(TAG, "=== rk3588开发板摄像头全面扫描 ===");
        String[] cameraIds = cameraManager.getCameraIdList();
        Log.i(TAG, "发现 " + cameraIds.length + " 个摄像头设备");
        
        if (cameraIds.length == 0) {
            throw new CameraAccessException(CameraAccessException.CAMERA_ERROR, "❌ 未找到任何摄像头设备");
        }
        
        // 扫描所有摄像头并评估
        CameraInfo[] cameraInfos = new CameraInfo[cameraIds.length];
        for (int i = 0; i < cameraIds.length; i++) {
            cameraInfos[i] = analyzeCameraDevice(cameraIds[i]);
        }
        
        // 选择最佳摄像头
        CameraInfo bestCamera = selectBestCamera(cameraInfos);
        cameraId = bestCamera.id;
        
        Log.i(TAG, "✅ 选择最佳摄像头: " + cameraId);
        Log.i(TAG, "选择原因: " + bestCamera.selectionReason);
        
        // 输出最终选择的摄像头详细信息
        logSelectedCameraInfo();
        Log.i(TAG, "=== rk3588摄像头选择完成 ===");
    }
    
    /**
     * 摄像头信息类
     */
    private static class CameraInfo {
        String id;
        String type;
        Integer facing;
        int maxWidth;
        int maxHeight;
        int resolutionCount;
        int score;
        String selectionReason;
        boolean isExternal;
        boolean hasHighRes;
        boolean hasGoodAspectRatio;
        Size[] allSizes;
        
        @Override
        public String toString() {
            return String.format("Camera[%s] %s %dx%d 分辨率数:%d 评分:%d", 
                id, type, maxWidth, maxHeight, resolutionCount, score);
        }
    }
    
    /**
     * 分析摄像头设备详细信息
     */
    private CameraInfo analyzeCameraDevice(String id) throws CameraAccessException {
        Log.i(TAG, "--- 分析摄像头设备: " + id + " ---");
        
        CameraInfo info = new CameraInfo();
        info.id = id;
        
        CameraCharacteristics characteristics = cameraManager.getCameraCharacteristics(id);
        info.facing = characteristics.get(CameraCharacteristics.LENS_FACING);
        
        // 获取支持的分辨率
        StreamConfigurationMap map = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
        if (map != null) {
            info.allSizes = map.getOutputSizes(SurfaceTexture.class);
            info.resolutionCount = info.allSizes != null ? info.allSizes.length : 0;
            
            // 计算最大分辨率
            if (info.allSizes != null && info.allSizes.length > 0) {
                for (Size size : info.allSizes) {
                    if (size.getWidth() > info.maxWidth) {
                        info.maxWidth = size.getWidth();
                        info.maxHeight = size.getHeight();
                    }
                }
            }
        }
        
        // 判断摄像头类型
        if (info.facing != null) {
            switch (info.facing) {
                case CameraCharacteristics.LENS_FACING_BACK:
                    info.type = "后置摄像头";
                    break;
                case CameraCharacteristics.LENS_FACING_FRONT:
                    info.type = "前置摄像头";
                    break;
                case CameraCharacteristics.LENS_FACING_EXTERNAL:
                    info.type = "外置摄像头";
                    info.isExternal = true;
                    break;
                default:
                    info.type = "其他类型摄像头";
                    break;
            }
        } else {
            // facing为null，在rk3588上通常是USB摄像头
            info.type = "USB摄像头";
            info.isExternal = true;
        }
        
        // 特征分析
        info.hasHighRes = info.maxWidth >= 1280;
        info.hasGoodAspectRatio = isGoodAspectRatio(info.maxWidth, info.maxHeight);
        
        // 评分计算
        info.score = calculateCameraScore(info);
        
        // 输出详细信息
        Log.i(TAG, "设备信息: " + info.toString());
        Log.i(TAG, "设备特征: 外置=" + info.isExternal + " 高分辨率=" + info.hasHighRes + " 好比例=" + info.hasGoodAspectRatio);
        
        // 输出支持的分辨率（前5个）
        if (info.allSizes != null && info.allSizes.length > 0) {
            Log.i(TAG, "支持的分辨率:");
            for (int i = 0; i < Math.min(5, info.allSizes.length); i++) {
                Size size = info.allSizes[i];
                Log.i(TAG, "  " + (i+1) + ": " + size.getWidth() + "x" + size.getHeight());
            }
            if (info.allSizes.length > 5) {
                Log.i(TAG, "  ... 还有 " + (info.allSizes.length - 5) + " 个分辨率");
            }
        }
        
        return info;
    }
    
    /**
     * 计算摄像头评分
     */
    private int calculateCameraScore(CameraInfo info) {
        int score = 0;
        
        // 1. 摄像头类型评分 (0-40分)
        if (info.isExternal) {
            score += 40; // 外置摄像头最高优先级
        } else if (info.facing == CameraCharacteristics.LENS_FACING_BACK) {
            score += 30; // 后置摄像头次之
        } else if (info.facing == CameraCharacteristics.LENS_FACING_FRONT) {
            score += 20; // 前置摄像头
        } else {
            score += 10; // 其他类型
        }
        
        // 2. 分辨率评分 (0-30分)
        if (info.maxWidth >= 1920) {
            score += 30; // 1080p或以上
        } else if (info.maxWidth >= 1280) {
            score += 25; // 720p
        } else if (info.maxWidth >= 1024) {
            score += 20; // 1024p
        } else if (info.maxWidth >= 640) {
            score += 15; // VGA
        } else {
            score += 5; // 低分辨率
        }
        
        // 3. 比例评分 (0-15分)
        if (info.hasGoodAspectRatio) {
            score += 15; // 16:9 或 4:3 比例
        } else {
            score += 5; // 其他比例
        }
        
        // 4. 分辨率数量评分 (0-10分)
        if (info.resolutionCount >= 10) {
            score += 10; // 支持多种分辨率
        } else if (info.resolutionCount >= 5) {
            score += 7;
        } else if (info.resolutionCount >= 3) {
            score += 5;
        } else {
            score += 2;
        }
        
        // 5. rk3588特殊加分 (0-5分)
        if (info.isExternal && info.hasHighRes) {
            score += 5; // rk3588外置高分辨率摄像头
        }
        
        return score;
    }
    
    /**
     * 判断是否为良好的宽高比
     */
    private boolean isGoodAspectRatio(int width, int height) {
        if (width <= 0 || height <= 0) return false;
        
        double ratio = (double) width / height;
        
        // 16:9 比例 (1.777)
        if (Math.abs(ratio - 16.0/9.0) < 0.1) return true;
        
        // 4:3 比例 (1.333)
        if (Math.abs(ratio - 4.0/3.0) < 0.1) return true;
        
        // 3:2 比例 (1.5)
        if (Math.abs(ratio - 3.0/2.0) < 0.1) return true;
        
        return false;
    }
    
    /**
     * 选择最佳摄像头
     */
    private CameraInfo selectBestCamera(CameraInfo[] cameraInfos) {
        Log.i(TAG, "=== 摄像头评分排序 ===");
        
        // 排序（评分从高到低）
        java.util.Arrays.sort(cameraInfos, (a, b) -> Integer.compare(b.score, a.score));
        
        // 输出所有摄像头评分
        for (int i = 0; i < cameraInfos.length; i++) {
            CameraInfo info = cameraInfos[i];
            Log.i(TAG, "排名 " + (i+1) + ": " + info.toString());
        }
        
        // 选择最高分的摄像头
        CameraInfo bestCamera = cameraInfos[0];
        
        // 生成选择原因
        StringBuilder reason = new StringBuilder();
        reason.append("评分最高(").append(bestCamera.score).append("分) - ");
        reason.append(bestCamera.type);
        if (bestCamera.hasHighRes) {
            reason.append(" + 高分辨率");
        }
        if (bestCamera.hasGoodAspectRatio) {
            reason.append(" + 标准比例");
        }
        if (bestCamera.isExternal) {
            reason.append(" + 外置设备");
        }
        
        bestCamera.selectionReason = reason.toString();
        
        Log.i(TAG, "=== 最佳摄像头选择 ===");
        Log.i(TAG, "选择: " + bestCamera.toString());
        Log.i(TAG, "原因: " + bestCamera.selectionReason);
        
        return bestCamera;
    }
    
    /**
     * 输出选择的摄像头详细信息
     */
    private void logSelectedCameraInfo() throws CameraAccessException {
        if (cameraId == null) return;
        
        Log.i(TAG, "=== 选择的摄像头详细信息 ===");
        CameraCharacteristics characteristics = cameraManager.getCameraCharacteristics(cameraId);
        
        // 基本信息
        Integer facing = characteristics.get(CameraCharacteristics.LENS_FACING);
        String facingStr = facing == null ? "null(外置USB)" : 
                          facing == CameraCharacteristics.LENS_FACING_BACK ? "后置" :
                          facing == CameraCharacteristics.LENS_FACING_FRONT ? "前置" :
                          facing == CameraCharacteristics.LENS_FACING_EXTERNAL ? "外置" : "其他";
        
        Log.i(TAG, "摄像头ID: " + cameraId);
        Log.i(TAG, "朝向: " + facingStr);
        
        // 支持的分辨率
        StreamConfigurationMap map = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
        if (map != null) {
            Size[] sizes = map.getOutputSizes(SurfaceTexture.class);
            if (sizes != null && sizes.length > 0) {
                Log.i(TAG, "支持的分辨率数量: " + sizes.length);
                // 显示前几个分辨率
                for (int i = 0; i < Math.min(3, sizes.length); i++) {
                    Log.i(TAG, "分辨率 " + (i+1) + ": " + sizes[i].getWidth() + "x" + sizes[i].getHeight());
                }
            }
        }
        
        Log.i(TAG, "=============================");
    }
    
    private void setupPreviewSize() throws CameraAccessException {
        CameraCharacteristics characteristics = cameraManager.getCameraCharacteristics(cameraId);
        StreamConfigurationMap map = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
        
        if (map == null) {
            throw new CameraAccessException(CameraAccessException.CAMERA_ERROR, "No stream configuration map");
        }
        
        Size[] sizes = map.getOutputSizes(SurfaceTexture.class);
        
        // 显示所有可用分辨率
        Log.i(TAG, "=== 可用分辨率列表 ===");
        for (int i = 0; i < Math.min(sizes.length, 10); i++) {
            Size size = sizes[i];
            Log.i(TAG, "  " + (i+1) + ": " + size.getWidth() + "x" + size.getHeight());
        }
        if (sizes.length > 10) {
            Log.i(TAG, "  ... 还有 " + (sizes.length - 10) + " 个分辨率");
        }
        
        previewSize = chooseOptimalSizeForRk3588(sizes, MAX_PREVIEW_WIDTH, MAX_PREVIEW_HEIGHT);
        
        Log.i(TAG, "=== 自适应预览尺寸选择结果 ===");
        Log.i(TAG, "✅ 最终选择的预览尺寸: " + previewSize.getWidth() + "x" + previewSize.getHeight());
        Log.i(TAG, "   总可用分辨率数量: " + sizes.length);
    }
    
    /**
     * 自适应分辨率选择策略
     * 优先选择1280x720，支持任意摄像头分辨率
     */
    private static Size chooseOptimalSizeForRk3588(Size[] choices, int maxWidth, int maxHeight) {
        Log.i(TAG, "=== 自适应分辨率选择策略 ===");
        
        // 🎯 最高优先级：强制选择1280x720
        for (Size choice : choices) {
            if (choice.getWidth() == 1280 && choice.getHeight() == 720) {
                Log.i(TAG, "✅ 优先选择1280x720分辨率");
                return choice;
            }
        }
        Log.w(TAG, "⚠️ 摄像头不支持1280x720，使用备选分辨率");
        
        // 常用的优质分辨率优先级（适合人脸检测）
        Size[] preferredSizes = {
            new Size(1920, 1080),  // 1080p - 外置摄像头常用
            new Size(1280, 720),   // 720p  - 性能平衡（已在上面检查）
            new Size(1024, 768),   // 4:3比例
            new Size(960, 720),    // 4:3比例
            new Size(640, 480),    // VGA - 兼容性好
        };
        
        // 1. 优先选择预设的优质分辨率
        for (Size preferred : preferredSizes) {
            for (Size choice : choices) {
                if (choice.getWidth() == preferred.getWidth() && 
                    choice.getHeight() == preferred.getHeight()) {
                    Log.i(TAG, "✅ 选择优质分辨率: " + choice.getWidth() + "x" + choice.getHeight());
                    return choice;
                }
            }
        }
        
        // 2. 选择最接近1080p的分辨率（外置摄像头通常支持）
        Size closestTo1080p = null;
        int minDiff = Integer.MAX_VALUE;
        
        for (Size choice : choices) {
            if (choice.getWidth() <= maxWidth && choice.getHeight() <= maxHeight) {
                int diff = Math.abs(choice.getWidth() - 1920) + Math.abs(choice.getHeight() - 1080);
                if (diff < minDiff) {
                    minDiff = diff;
                    closestTo1080p = choice;
                }
            }
        }
        
        if (closestTo1080p != null) {
            Log.i(TAG, "✅ 选择最接近1080p的分辨率: " + closestTo1080p.getWidth() + "x" + closestTo1080p.getHeight());
            return closestTo1080p;
        }
        
        // 3. 选择最大可用分辨率
        Size maxSize = choices[0];
        for (Size choice : choices) {
            if (choice.getWidth() <= maxWidth && choice.getHeight() <= maxHeight) {
                if (choice.getWidth() * choice.getHeight() > maxSize.getWidth() * maxSize.getHeight()) {
                    maxSize = choice;
                }
            }
        }
        
        Log.i(TAG, "✅ 选择最大可用分辨率: " + maxSize.getWidth() + "x" + maxSize.getHeight());
        return maxSize;
    }
    
    /**
     * 设置ImageReader用于后台数据处理
     */
    public void setupImageReader(int width, int height, ImageReader.OnImageAvailableListener listener) {
        // 停止并清理旧的 ImageReader (如果有)
        if (imageReader != null) {
            imageReader.close();
        }
        if (imageReaderThread != null) {
            imageReaderThread.quitSafely();
        }
        
        // 创建一个新的 ImageReader 用于在后台获取图像数据
        imageReader = ImageReader.newInstance(width, height, android.graphics.ImageFormat.YUV_420_888, 2);
        
        // 为 ImageReader 的回调创建一个专用的后台线程
        imageReaderThread = new HandlerThread("ImageReaderThread");
        imageReaderThread.start();
        imageReaderHandler = new Handler(imageReaderThread.getLooper());
        
        // 设置监听器，它将在 imageReaderHandler 所在的后台线程上被调用
        imageReader.setOnImageAvailableListener(listener, imageReaderHandler);
        
        Log.i(TAG, "ImageReader 设置完成，将在后台线程处理数据");
    }
    
    /**
     * 打开摄像头
     */
    public boolean openCamera(SurfaceTexture surfaceTexture) {
        if (!checkCameraPermission()) {
            Log.e(TAG, "× No camera permission");
            return false;
        }
        
        this.surfaceTexture = surfaceTexture;
        setupSurface();
        startBackgroundThread();
        
        try {
            Log.i(TAG, "尝试打开rk3588摄像头: " + cameraId);
            if (!cameraOpenCloseLock.tryAcquire(RK3588_CAMERA_OPEN_TIMEOUT, TimeUnit.MILLISECONDS)) {
                throw new RuntimeException("rk3588摄像头打开超时，等待时间: " + RK3588_CAMERA_OPEN_TIMEOUT + "ms");
            }
            cameraManager.openCamera(cameraId, stateCallback, backgroundHandler);
            Log.i(TAG, "rk3588摄像头打开请求已发送");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "× rk3588摄像头打开失败", e);
            return false;
        }
    }
    
    private boolean checkCameraPermission() {
        return ContextCompat.checkSelfPermission(context, Manifest.permission.CAMERA) 
            == PackageManager.PERMISSION_GRANTED;
    }
    
    private void setupSurface() {
        if (surfaceTexture != null && previewSize != null) {
            surfaceTexture.setDefaultBufferSize(previewSize.getWidth(), previewSize.getHeight());
            previewSurface = new Surface(surfaceTexture);
        }
    }
    
    /**
     * 关闭摄像头
     */
    public void closeCamera() {
        try {
            cameraOpenCloseLock.acquire();
            if (captureSession != null) {
                captureSession.close();
                captureSession = null;
            }
            if (cameraDevice != null) {
                cameraDevice.close();
                cameraDevice = null;
            }
            if (previewSurface != null) {
                previewSurface.release();
                previewSurface = null;
            }
            if (imageReader != null) {
                imageReader.close();
                imageReader = null;
            }
        } catch (InterruptedException e) {
            throw new RuntimeException("Interrupted while trying to lock camera closing.", e);
        } finally {
            cameraOpenCloseLock.release();
        }
        stopBackgroundThread();
    }
    
    /**
     * 开始预览
     */
    public boolean startPreview() {
        if (cameraDevice == null || previewSurface == null) {
            Log.e(TAG, "× Preview conditions not met");
            return false;
        }
        
        try {
            previewRequestBuilder = cameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW);
            previewRequestBuilder.addTarget(previewSurface);
            
            // 同时将预览数据发送到ImageReader
            if (imageReader != null) {
                previewRequestBuilder.addTarget(imageReader.getSurface());
            }
            
            // 准备所有的Surface
            java.util.List<Surface> surfaces = new java.util.ArrayList<>();
            surfaces.add(previewSurface);
            if (imageReader != null) {
                surfaces.add(imageReader.getSurface());
            }
            
            cameraDevice.createCaptureSession(
                surfaces,
                new CameraCaptureSession.StateCallback() {
                    @Override
                    public void onConfigured(CameraCaptureSession cameraCaptureSession) {
                        if (cameraDevice == null) return;
                        
                        captureSession = cameraCaptureSession;
                        updatePreview();
                        
                        Log.i(TAG, "✅ rk3588摄像头预览配置成功");
                        if (callback != null) {
                            callback.onPreviewStarted();
                        }
                    }
                    
                    @Override
                    public void onConfigureFailed(CameraCaptureSession cameraCaptureSession) {
                        Log.e(TAG, "× rk3588摄像头预览配置失败");
                    }
                },
                null
            );
            return true;
        } catch (CameraAccessException e) {
            Log.e(TAG, "× Failed to start preview", e);
            return false;
        }
    }
    
    private void updatePreview() {
        if (cameraDevice == null) return;
        
        try {
            previewRequestBuilder.set(CaptureRequest.CONTROL_MODE, CaptureRequest.CONTROL_MODE_AUTO);
            previewRequest = previewRequestBuilder.build();
            captureSession.setRepeatingRequest(previewRequest, null, backgroundHandler);
        } catch (CameraAccessException e) {
            Log.e(TAG, "× Failed to update preview", e);
        }
    }
    
    private void startBackgroundThread() {
        backgroundThread = new HandlerThread("CameraBackground");
        backgroundThread.start();
        backgroundHandler = new Handler(backgroundThread.getLooper());
    }
    
    private void stopBackgroundThread() {
        if (backgroundThread != null) {
            backgroundThread.quitSafely();
            try {
                backgroundThread.join();
                backgroundThread = null;
                backgroundHandler = null;
            } catch (InterruptedException e) {
                Log.e(TAG, "× Interrupted while stopping background thread", e);
            }
        }
        
        if (imageReaderThread != null) {
            imageReaderThread.quitSafely();
            try {
                imageReaderThread.join();
                imageReaderThread = null;
                imageReaderHandler = null;
            } catch (InterruptedException e) {
                Log.e(TAG, "× Interrupted while stopping image reader thread", e);
            }
        }
    }
    
    // Getters
    public Size getPreviewSize() {
        return previewSize;
    }
    
    public boolean isInitialized() {
        return cameraId != null && previewSize != null;
    }
    
    public boolean isCameraOpen() {
        return cameraDevice != null;
    }
    
    /**
     * 获取当前摄像头分辨率
     */
    public android.util.Size getCameraResolution() {
        if (previewSize != null) {
            // 更新缓存
            lastKnownResolution = new android.util.Size(previewSize.getWidth(), previewSize.getHeight());
            Log.d(TAG, "✅ 返回当前摄像头分辨率: " + previewSize.getWidth() + "x" + previewSize.getHeight());
            return lastKnownResolution;
        }
        
        // 如果当前previewSize为null，但有缓存的分辨率，使用缓存
        if (lastKnownResolution != null) {
            Log.d(TAG, "⚠️ 使用缓存的摄像头分辨率: " + lastKnownResolution.getWidth() + "x" + lastKnownResolution.getHeight());
            return lastKnownResolution;
        }
        
        Log.w(TAG, "⚠️ 预览尺寸和缓存都未设置，返回null");
        return null;
    }
    
    /**
     * 获取最后已知的分辨率（用于调试）
     */
    public android.util.Size getLastKnownResolution() {
        return lastKnownResolution;
    }
    
    /**
     * 获取当前摄像头ID
     */
    public String getCurrentCameraId() {
        return cameraId;
    }
    
    /**
     * 释放资源
     */
    public void release() {
        closeCamera();
    }
} 