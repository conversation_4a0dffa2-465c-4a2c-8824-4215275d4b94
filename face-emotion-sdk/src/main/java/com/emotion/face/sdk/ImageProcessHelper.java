package com.emotion.face.sdk;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.media.Image;
import android.renderscript.Allocation;
import android.renderscript.Element;
import android.renderscript.RenderScript;
import android.renderscript.ScriptIntrinsicYuvToRGB;
import android.renderscript.Type;
import android.util.Log;
import java.nio.ByteBuffer;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 图像处理辅助类
 * 提供高效的图像处理功能和内存管理
 */
public class ImageProcessHelper {
    
    private static final String TAG = "ImageProcessHelper";
    
    // Buffer池配置 - RGB格式，由C++层处理
    private static final int BUFFER_SIZE = 640 * 640 * 3; // RGB: 3字节/像素
    private static final int MAX_POOL_SIZE = 8;
    private static final int MIN_POOL_SIZE = 2;
    
    // Buffer池
    private static final ConcurrentLinkedQueue<ByteBuffer> bufferPool = new ConcurrentLinkedQueue<>();
    private static final AtomicInteger poolSize = new AtomicInteger(0);
    private static final AtomicInteger poolHit = new AtomicInteger(0);
    private static final AtomicInteger poolMiss = new AtomicInteger(0);
    
    // Bitmap池
    private static final ConcurrentLinkedQueue<Bitmap> bitmapPool = new ConcurrentLinkedQueue<>();
    private static final AtomicInteger bitmapPoolSize = new AtomicInteger(0);
    
    // RenderScript 相关变量
    private static RenderScript rs;
    private static ScriptIntrinsicYuvToRGB yuvToRgbIntrinsic;
    private static Type.Builder yuvType, rgbaType;
    private static Allocation in, out;
    
    // 重用对象
    private static final Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private static final Matrix matrix = new Matrix();
    private static final Rect srcRect = new Rect();
    private static final Rect dstRect = new Rect();
    
    static {
        // 预初始化Buffer池
        initializeBufferPool();
    }
    
    private static void initializeBufferPool() {
        for (int i = 0; i < MIN_POOL_SIZE; i++) {
            ByteBuffer buffer = ByteBuffer.allocateDirect(BUFFER_SIZE);
            bufferPool.offer(buffer);
            poolSize.incrementAndGet();
        }
    }
    
    /**
     * 初始化RenderScript
     */
    public static void initialize(Context context) {
        if (rs == null) {
            rs = RenderScript.create(context);
            yuvToRgbIntrinsic = ScriptIntrinsicYuvToRGB.create(rs, Element.U8_4(rs));
            Log.i(TAG, "RenderScript initialized for YUV to RGB conversion");
        }
    }
    
    /**
     * 获取ByteBuffer
     */
    public static ByteBuffer obtainBuffer() {
        ByteBuffer buffer = bufferPool.poll();
        if (buffer != null) {
            poolHit.incrementAndGet();
            poolSize.decrementAndGet();
            buffer.clear();
            return buffer;
        }
        
        poolMiss.incrementAndGet();
        // 只在池未命中率过高时输出警告
        if (poolMiss.get() % 50 == 0) {
            int hitRate = poolHit.get() * 100 / (poolHit.get() + poolMiss.get());
            if (hitRate < 80) {
                Log.w(TAG, "× Buffer pool hit rate: " + hitRate + "%");
            }
        }
        
        return ByteBuffer.allocateDirect(BUFFER_SIZE);
    }
    
    /**
     * 回收ByteBuffer
     */
    public static void recycleBuffer(ByteBuffer buffer) {
        if (buffer == null || !buffer.isDirect()) return;
        
        if (poolSize.get() < MAX_POOL_SIZE) {
            buffer.clear();
            bufferPool.offer(buffer);
            poolSize.incrementAndGet();
        }
    }
    
    /**
     * 获取Bitmap - 零分配热路径优化
     */
    public static Bitmap obtainBitmap(int width, int height) {
        Bitmap bitmap = bitmapPool.poll();
        if (bitmap != null && !bitmap.isRecycled()) {
            bitmapPoolSize.decrementAndGet();
            
            // 检查尺寸匹配
            if (bitmap.getWidth() == width && bitmap.getHeight() == height) {
                bitmap.eraseColor(0); // 清空bitmap内容
                return bitmap;
            } else {
                // 尺寸不匹配，回收后创建新的
                bitmap.recycle();
            }
        }
        
        return Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
    }
    
    /**
     * 回收Bitmap
     */
    public static void recycleBitmap(Bitmap bitmap) {
        if (bitmap == null || bitmap.isRecycled()) return;
        
        if (bitmapPoolSize.get() < 4) { // 限制Bitmap池大小
            bitmapPool.offer(bitmap);
            bitmapPoolSize.incrementAndGet();
        } else {
            bitmap.recycle();
        }
    }
    
    /**
     * 将Bitmap转换为RGB格式的ByteBuffer
     * 输出的ByteBuffer是RGB格式(3字节/像素)，适合模型输入
     * 
     * @param sourceBitmap 源Bitmap (ARGB格式)
     * @param destinationBuffer 目标ByteBuffer (固定640x640x3大小，RGB格式)
     */
    public static void bitmapToBuffer(Bitmap sourceBitmap, ByteBuffer destinationBuffer) {
        if (sourceBitmap == null || destinationBuffer == null) {
            Log.e(TAG, "bitmapToBuffer: 输入参数为null");
            return;
        }
        
        // 保持宽高比的智能缩放到640x640
        Bitmap processedBitmap = sourceBitmap;
        if (sourceBitmap.getWidth() != 640 || sourceBitmap.getHeight() != 640) {
            Log.d(TAG, "缩放图像：" + sourceBitmap.getWidth() + "x" + sourceBitmap.getHeight() + " → 640x640 (保持宽高比)");
            processedBitmap = scaleToSquareWithAspectRatio(sourceBitmap, 640);
        } else {
            Log.d(TAG, "图像尺寸已经是640x640，直接处理");
        }
        
        try {
            // 将ARGB Bitmap转换为RGB ByteBuffer
            int width = processedBitmap.getWidth();
            int height = processedBitmap.getHeight();
            int[] pixels = new int[width * height];
            processedBitmap.getPixels(pixels, 0, width, 0, 0, width, height);
            
            // 清空目标缓冲区
            destinationBuffer.clear();
            
            // 转换ARGB到RGB
            for (int pixel : pixels) {
                destinationBuffer.put((byte) ((pixel >> 16) & 0xFF)); // R
                destinationBuffer.put((byte) ((pixel >> 8) & 0xFF));  // G
                destinationBuffer.put((byte) (pixel & 0xFF));         // B
                // 跳过Alpha通道
            }
            
        destinationBuffer.flip();
            Log.d(TAG, "✅ ARGB→RGB转换完成，ByteBuffer大小: " + destinationBuffer.capacity() + " 字节");
            
        } finally {
            // 回收临时缩放的Bitmap
            if (processedBitmap != sourceBitmap) {
                processedBitmap.recycle();
            }
        }
    }
    

    
    /**
     * 将YUV_420_888格式的Image转换为ARGB Bitmap
     * 
     * @param image YUV_420_888格式的Image
     * @param outputBitmap 输出的ARGB Bitmap
     */
    public static void yuvToBitmap(Image image, Bitmap outputBitmap) {
        if (rs == null) {
            Log.e(TAG, "RenderScript not initialized. Call ImageProcessHelper.initialize() first.");
            return;
        }
        
        // Get the YUV data from the image planes
        Image.Plane[] planes = image.getPlanes();
        ByteBuffer yBuffer = planes[0].getBuffer();
        ByteBuffer uBuffer = planes[1].getBuffer();
        ByteBuffer vBuffer = planes[2].getBuffer();
        
        int ySize = yBuffer.remaining();
        int uSize = uBuffer.remaining();
        int vSize = vBuffer.remaining();
        
        byte[] yuvBytes = new byte[ySize + uSize + vSize];
        yBuffer.get(yuvBytes, 0, ySize);
        vBuffer.get(yuvBytes, ySize, vSize); // Note: V comes before U in NV21
        uBuffer.get(yuvBytes, ySize + vSize, uSize);
        
        // Create RenderScript allocations
        if (yuvType == null) {
            yuvType = new Type.Builder(rs, Element.U8(rs)).setX(yuvBytes.length);
            in = Allocation.createTyped(rs, yuvType.create(), Allocation.USAGE_SCRIPT);
            
            rgbaType = new Type.Builder(rs, Element.RGBA_8888(rs)).setX(image.getWidth()).setY(image.getHeight());
            out = Allocation.createTyped(rs, rgbaType.create(), Allocation.USAGE_SCRIPT);
        }
        
        // Copy the YUV data to the input allocation
        in.copyFrom(yuvBytes);
        
        // Set the input allocation to the script
        yuvToRgbIntrinsic.setInput(in);
        
        // Run the script to convert to RGB
        yuvToRgbIntrinsic.forEach(out);
        
        // Copy the result to the output bitmap
        out.copyTo(outputBitmap);
    }
    
    /**
     * 将Bitmap缩放到正方形，保持宽高比，用填充代替裁剪 (避免边缘人脸丢失)
     * @param sourceBitmap 源图像  
     * @param targetSize 目标尺寸 (如640)
     * @return 缩放+填充后的正方形Bitmap
     */
    private static Bitmap scaleToSquareWithAspectRatio(Bitmap sourceBitmap, int targetSize) {
        int sourceWidth = sourceBitmap.getWidth();
        int sourceHeight = sourceBitmap.getHeight();
        
        // 计算缩放比例 - 让较长的边缩放到targetSize (保证完整内容都在640x640内)
        float scale = Math.min((float) targetSize / sourceWidth, (float) targetSize / sourceHeight);
        
        // 缩放后的尺寸
        int scaledWidth = Math.round(sourceWidth * scale);
        int scaledHeight = Math.round(sourceHeight * scale);
        
        // 第一步：保持宽高比缩放
        Bitmap scaledBitmap = Bitmap.createScaledBitmap(sourceBitmap, scaledWidth, scaledHeight, true);
        
        // 第二步：创建640x640的画布，用黑色填充
        Bitmap resultBitmap = Bitmap.createBitmap(targetSize, targetSize, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(resultBitmap);
        canvas.drawColor(android.graphics.Color.BLACK); // 黑色背景填充
        
        // 第三步：将缩放后的图像居中绘制到画布上
        int offsetX = (targetSize - scaledWidth) / 2;
        int offsetY = (targetSize - scaledHeight) / 2;
        canvas.drawBitmap(scaledBitmap, offsetX, offsetY, null);
        
        // 回收中间Bitmap
        scaledBitmap.recycle();
        
        return resultBitmap;
    }

    
    /**
     * 在Bitmap上绘制人脸框
     */
    public static void drawFaceBoxes(Bitmap bitmap, FaceBox[] faces) {
        if (bitmap == null || faces == null || faces.length == 0) {
            return;
        }
        
        Canvas canvas = new Canvas(bitmap);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(3f);
        
        for (FaceBox face : faces) {
            if (face != null) {
                // 设置颜色
                switch (face.emotion != null ? face.emotion : "unknown") {
                    case "happy":
                        paint.setColor(0xFF00FF00); // 绿色
                        break;
                    case "angry":
                        paint.setColor(0xFFFF0000); // 红色
                        break;
                    case "sad":
                        paint.setColor(0xFF0000FF); // 蓝色
                        break;
                    default:
                        paint.setColor(0xFFFFFF00); // 黄色
                        break;
                }
                
                // 绘制矩形
                canvas.drawRect(face.x1, face.y1, face.x2, face.y2, paint);
            }
        }
    }
    
    /**
     * 裁剪人脸区域
     */
    public static Bitmap cropFace(Bitmap source, FaceBox face, int padding) {
        if (source == null || face == null) {
            return null;
        }
        
        int x = Math.max(0, (int) face.x1 - padding);
        int y = Math.max(0, (int) face.y1 - padding);
        int width = Math.min(source.getWidth() - x, (int) (face.x2 - face.x1) + 2 * padding);
        int height = Math.min(source.getHeight() - y, (int) (face.y2 - face.y1) + 2 * padding);
        
        if (width <= 0 || height <= 0) {
            return null;
        }
        
        try {
            return Bitmap.createBitmap(source, x, y, width, height);
        } catch (Exception e) {
            Log.e(TAG, "× Failed to crop face", e);
            return null;
        }
    }
    
    /**
     * 获取池统计信息
     */
    public static String getPoolStats() {
        int total = poolHit.get() + poolMiss.get();
        int hitRate = total > 0 ? poolHit.get() * 100 / total : 0;
        
        return String.format("Buffer pool: %d/%d, hit: %d%%, bitmap: %d", 
            poolSize.get(), MAX_POOL_SIZE, hitRate, bitmapPoolSize.get());
    }
    
    /**
     * 清理资源 - 完整版本
     */
    public static void cleanup() {
        // 清理Buffer池
        ByteBuffer buffer;
        while ((buffer = bufferPool.poll()) != null) {
            // ByteBuffer会被GC自动回收
        }
        poolSize.set(0);
        
        // 清理Bitmap池
        Bitmap bitmap;
        while ((bitmap = bitmapPool.poll()) != null) {
            if (!bitmap.isRecycled()) {
                bitmap.recycle();
            }
        }
        bitmapPoolSize.set(0);
        
        // 清理RenderScript资源
        if (rs != null) {
            if (in != null) {
                in.destroy();
                in = null;
            }
            if (out != null) {
                out.destroy();
                out = null;
            }
            if (yuvToRgbIntrinsic != null) {
                yuvToRgbIntrinsic.destroy();
                yuvToRgbIntrinsic = null;
            }
            rs.destroy();
            rs = null;
            yuvType = null;
            rgbaType = null;
            Log.i(TAG, "RenderScript resources cleaned up");
        }
        
        // 重置统计
        poolHit.set(0);
        poolMiss.set(0);
        
        Log.i(TAG, "ImageProcessHelper资源清理完成");
    }
} 