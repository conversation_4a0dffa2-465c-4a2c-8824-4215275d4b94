package com.emotion.face.sdk;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 人脸检测显示视图
 * 用于在UI上显示人脸检测框和情感结果
 */
public class FaceDetectionView extends View {
    
    private static final String TAG = "FaceDetectionView";
    
    // 绘制工具
    private final Paint faceBoxPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private final Paint textPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private final RectF tempRect = new RectF();
    
    // 字符串池 - 避免频繁创建
    private final StringBuilder reusableStringBuilder = new StringBuilder(32);
    private final char[] tempCharArray = new char[16];
    
    // 情感颜色映射 - 避免每次onDraw中的switch-case开销
    private final Map<String, Integer> emotionColorMap = new HashMap<>();
    
    // 检测数据 - 双缓冲结构（使用对象池管理的List）
    private List<FaceBox> frontBuffer;
    private List<FaceBox> backBuffer;
    private final Object bufferLock = new Object();
    private volatile boolean hasNewData = false;
    
    // 显示配置
    private float scaleX = 1.0f;
    private float scaleY = 1.0f;
    private boolean showConfidence = true;
    private boolean showEmotion = true;
    
    public FaceDetectionView(Context context) {
        super(context);
        init();
    }
    
    public FaceDetectionView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public FaceDetectionView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        // 配置人脸框绘制
        faceBoxPaint.setStyle(Paint.Style.STROKE);
        faceBoxPaint.setStrokeWidth(3f);
        faceBoxPaint.setColor(0xFF00FF00); // 默认绿色
        
        // 配置文本绘制
        textPaint.setStyle(Paint.Style.FILL);
        textPaint.setTextSize(32f);
        textPaint.setColor(0xFFFFFFFF);
        textPaint.setShadowLayer(2f, 1f, 1f, 0xFF000000);
        
        // 预先填充情感颜色映射，避免onDraw中的switch-case开销
        // 底层实际返回: ["Angry", "Disgusted", "Fearful", "Happy", "Neutral", "Sad", "Surprised"]
        emotionColorMap.put("Happy", 0xFF00FF00);      // 绿色
        emotionColorMap.put("Sad", 0xFF0000FF);        // 蓝色
        emotionColorMap.put("Angry", 0xFFFF0000);      // 红色
        emotionColorMap.put("Fearful", 0xFF800080);    // 紫色
        emotionColorMap.put("Surprised", 0xFFFF8C00);  // 橙色
        emotionColorMap.put("Disgusted", 0xFF8B4513);  // 棕色
        emotionColorMap.put("Neutral", 0xFF808080);    // 灰色
        
        // 为了兼容性，也添加小写版本
        emotionColorMap.put("happy", 0xFF00FF00);      // 绿色
        emotionColorMap.put("sad", 0xFF0000FF);        // 蓝色
        emotionColorMap.put("angry", 0xFFFF0000);      // 红色
        emotionColorMap.put("fearful", 0xFF800080);    // 紫色
        emotionColorMap.put("surprised", 0xFFFF8C00);  // 橙色
        emotionColorMap.put("disgusted", 0xFF8B4513);  // 棕色
        emotionColorMap.put("neutral", 0xFF808080);    // 灰色
        
        // 使用对象池初始化双缓冲区
        frontBuffer = FaceBoxPool.obtainList();
        backBuffer = FaceBoxPool.obtainList();
    }
    
    /**
     * 更新人脸检测结果 - 零内存分配版本
     */
    public void updateFaces(List<FaceBox> faces) {
        synchronized (bufferLock) {
            if (faces != null) {
                // 1. 回收旧的后台缓冲区中的所有FaceBox对象
                FaceBoxPool.recycleFaceBoxList(this.backBuffer);
                
                // 2. 将后台缓冲区本身也回收到List池中
                FaceBoxPool.recycleList(this.backBuffer);
                
                // 3. 直接将后台缓冲区指向新的数据源。
                // 注意：这里是直接交换引用，调用者传递的'faces'列表的所有权被转移到了FaceDetectionView。
                this.backBuffer = faces;
                
                // 4. 请求重绘
                this.hasNewData = true;
                postInvalidate();
            }
        }
    }
    
    /**
     * 清除显示 - 零内存分配版本
     */
    public void clearFaces() {
        synchronized (bufferLock) {
            // 回收前台缓冲区中的所有FaceBox对象
            FaceBoxPool.recycleFaceBoxList(this.frontBuffer);
            // 回收后台缓冲区中的所有FaceBox对象
            FaceBoxPool.recycleFaceBoxList(this.backBuffer);
            // 清空缓冲区，但保留List实例供复用
            this.frontBuffer.clear();
            this.backBuffer.clear();
            this.hasNewData = false;
        }
        invalidate();
    }
    
    /**
     * 设置缩放比例
     */
    public void setScale(float scaleX, float scaleY) {
        this.scaleX = scaleX;
        this.scaleY = scaleY;
        if (hasNewData) {
            invalidate();
        }
    }
    
    /**
     * 设置显示选项
     */
    public void setDisplayOptions(boolean showConfidence, boolean showEmotion) {
        this.showConfidence = showConfidence;
        this.showEmotion = showEmotion;
        if (hasNewData) {
            invalidate();
        }
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        // 在同步块中交换前后缓冲区
        synchronized (bufferLock) {
            // 交换 frontBuffer 和 backBuffer 的指针
            List<FaceBox> temp = this.frontBuffer;
            this.frontBuffer = this.backBuffer;
            this.backBuffer = temp;
            
            this.hasNewData = false; // 在UI线程中重置标志位
        }
        
        // 使用 frontBuffer 进行绘制
        if (frontBuffer == null || frontBuffer.isEmpty()) {
            return;
        }
        
        try {
            for (FaceBox face : frontBuffer) {
                if (face != null) {
                    drawFaceBox(canvas, face);
                }
            }
        } catch (Exception e) {
            // 只在错误时创建字符串
            Log.e(TAG, "Error drawing faces", e);
        }
    }
    
    private void drawFaceBox(Canvas canvas, FaceBox face) {
        // 坐标已经在FaceEmotionModule中转换完成，直接使用
        float left = face.x1;
        float top = face.y1;
        float right = face.x2;
        float bottom = face.y2;
        
        // 设置颜色（根据情感）
        setFaceBoxColor(face.emotion);
        
        // 绘制人脸框
        tempRect.set(left, top, right, bottom);
        canvas.drawRect(tempRect, faceBoxPaint);
        
        // 绘制标签
        if (showEmotion || showConfidence) {
            drawFaceLabel(canvas, face, left, top);
        }
    }
    
    private void setFaceBoxColor(String emotion) {
        // 使用HashMap查找替换switch-case，提高onDraw性能
        // 支持底层返回的大写情感名称: ["Angry", "Disgusted", "Fearful", "Happy", "Neutral", "Sad", "Surprised"]
        int color;
        if (emotion != null) {
            color = emotionColorMap.getOrDefault(emotion, 0xFFFFFF00); // 黄色 - 未知情感
        } else {
            color = 0xFFFFFF00; // 黄色 - null情感
        }
        faceBoxPaint.setColor(color);
    }
    
    private void drawFaceLabel(Canvas canvas, FaceBox face, float x, float y) {
        // 使用重用的StringBuilder - 避免每帧创建
        reusableStringBuilder.setLength(0);
        
        if (showEmotion && face.emotion != null) {
            reusableStringBuilder.append(face.emotion);
        }
        
        if (showConfidence && face.confidence > 0) {
            if (reusableStringBuilder.length() > 0) {
                reusableStringBuilder.append(' ');
            }
            // 避免String.format - 手动格式化
            int confidencePercent = (int) (face.confidence * 100);
            reusableStringBuilder.append(confidencePercent).append('%');
        }
        
        if (reusableStringBuilder.length() > 0) {
            // 绘制在人脸框上方
            float textY = Math.max(y - 10, textPaint.getTextSize());
            canvas.drawText(reusableStringBuilder.toString(), x, textY, textPaint);
        }
    }
    
    /**
     * 获取当前人脸数量
     */
    public int getFaceCount() {
        synchronized (bufferLock) {
            return frontBuffer != null ? frontBuffer.size() : 0;
        }
    }
    
    /**
     * 检查是否有检测结果
     */
    public boolean hasFaces() {
        synchronized (bufferLock) {
            return frontBuffer != null && !frontBuffer.isEmpty();
        }
    }
    
    /**
     * 释放所有资源到对象池 - 防止内存泄漏
     */
    public void release() {
        synchronized (bufferLock) {
            if (frontBuffer != null) {
                FaceBoxPool.recycleFaceBoxList(frontBuffer);
                FaceBoxPool.recycleList(frontBuffer);
                frontBuffer = null;
            }
            if (backBuffer != null) {
                FaceBoxPool.recycleFaceBoxList(backBuffer);
                FaceBoxPool.recycleList(backBuffer);
                backBuffer = null;
            }
            hasNewData = false;
        }
    }
} 