package com.emotion.face.sdk;

import java.nio.ByteBuffer;
import java.util.List;

/**
 * 人脸情感检测接口
 * 负责人脸检测和情感识别功能
 */
public class FaceEmotionInterface {
    
    private static final String TAG = "FaceEmotionInterface";
    private static boolean libraryLoaded = false;
    private boolean modelInitialized = false;
    
    // 加载本地库
    static {
        try {
            // android.util.Log.i(TAG, "开始加载人脸检测SDK库...");
            
            // 先加载依赖库
            // android.util.Log.d(TAG, "1. 加载依赖库 rknnrt...");
            System.loadLibrary("rknnrt");
            // android.util.Log.d(TAG, "✅ rknnrt 加载成功");
            
            // android.util.Log.d(TAG, "2. 加载依赖库 rga...");
            System.loadLibrary("rga");
            // android.util.Log.d(TAG, "✅ rga 加载成功");
            
            // 再加载主功能库
            // android.util.Log.d(TAG, "3. 加载主功能库 rknn_emotion_detection_demo...");
            System.loadLibrary("rknn_emotion_detection_demo");
            // android.util.Log.d(TAG, "✅ rknn_emotion_detection_demo 加载成功");
            
            // 加载JNI包装库
            // android.util.Log.d(TAG, "4. 加载JNI包装库 face_emotion_detection...");
            System.loadLibrary("face_emotion_detection");
            // android.util.Log.d(TAG, "✅ face_emotion_detection 加载成功");
            
            libraryLoaded = true;
            // android.util.Log.i(TAG, "✅ 人脸检测SDK库加载完成");
        } catch (UnsatisfiedLinkError e) {
            libraryLoaded = false;
            android.util.Log.e(TAG, "❌ 人脸检测SDK库加载失败: " + e.getMessage());
            android.util.Log.e(TAG, "库加载详细错误", e);
        }
    }

    /**
     * 底层JNI函数 - 人脸模块初始化
     * 对应C++函数: Java_com_emotion_face_sdk_FaceEmotionInterface_initRknnModel
     */
    public native String initRknnModel(String faceDetectionModelPath, String emotionDetectionModelPath);
    
    /**
     * 人脸模块初始化 - 需要两个模型路径
     * @param face_detection_model_path 人脸检测模型路径
     * @param emotion_detection_model_path 情感检测模型路径
     * @return 初始化是否成功
     */
    public boolean initModel(String face_detection_model_path,
                            String emotion_detection_model_path) {
        android.util.Log.i(TAG, "开始初始化人脸模型...");
        
        if (!libraryLoaded) {
            android.util.Log.e(TAG, "❌ 库未加载，无法初始化模型");
            return false;
        }
        
        android.util.Log.d(TAG, "人脸检测模型路径: " + face_detection_model_path);
        android.util.Log.d(TAG, "情感识别模型路径: " + emotion_detection_model_path);
        
        try {
            android.util.Log.d(TAG, "调用JNI方法 initRknnModel...");
            String result = initRknnModel(face_detection_model_path, emotion_detection_model_path);
            
            android.util.Log.d(TAG, "JNI返回结果: " + result);
            
            boolean success = result != null && result.contains("Success");
            if (success) {
                modelInitialized = true;
                android.util.Log.i(TAG, "✅ 人脸模型初始化成功");
            } else {
                modelInitialized = false;
                android.util.Log.e(TAG, "❌ 人脸模型初始化失败: " + result);
                android.util.Log.i(TAG, "继续运行，但人脸检测功能将不可用");
            }
            return success;
        } catch (UnsatisfiedLinkError e) {
            modelInitialized = false;
            android.util.Log.e(TAG, "❌ JNI方法未找到: " + e.getMessage());
            android.util.Log.i(TAG, "可能是C++库未正确加载或方法名不匹配，继续运行但人脸检测功能将不可用");
            return false;
        } catch (Exception e) {
            modelInitialized = false;
            android.util.Log.e(TAG, "❌ 人脸模型初始化异常: " + e.getMessage());
            android.util.Log.e(TAG, "详细异常信息", e);
            android.util.Log.i(TAG, "继续运行，但人脸检测功能将不可用");
            return false;
        }
    }
    
    /**
     * 检查库是否加载成功
     */
    public boolean isLibraryLoaded() {
        return libraryLoaded;
    }
    
    /**
     * 检测人脸并识别情感
     * 对应C++函数: Java_com_emotion_face_sdk_FaceEmotionInterface_detectFace
     * @param picBuffer 640x640x3的RGB图像数据ByteBuffer (Direct ByteBuffer)
     * @param outputRgbBuffer 用于C++层RGB数据处理的ByteBuffer
     * @return FaceBox数组，包含人脸位置和情感结果
     */
    public native FaceBox[] detectFace(ByteBuffer picBuffer, ByteBuffer outputRgbBuffer);
    
    /**
     * 优化版人脸检测 - 直接填充到List中，避免数组创建开销
     * 对应C++函数: Java_com_emotion_face_sdk_FaceEmotionInterface_detectFaceOptimized
     * @param picBuffer 640x640x3的RGB图像数据ByteBuffer (Direct ByteBuffer)
     * @param resultList 用于存储检测结果的List，会被清空后填入新结果
     * @param outputRgbBuffer 用于C++层RGB数据处理的ByteBuffer
     * @return 检测到的人脸数量
     */
    public native int detectFaceOptimized(ByteBuffer picBuffer, List<FaceBox> resultList, ByteBuffer outputRgbBuffer);
    
    /**
     * 释放人脸检测模块资源
     * 对应C++函数: Java_com_emotion_face_sdk_FaceEmotionInterface_releaseFaceModel
     */
    public native void releaseFaceModel();
    
    /**
     * 安全的释放人脸检测模块资源
     */
    public void releaseFaceModelSafe() {
        if (!libraryLoaded) {
            // android.util.Log.w(TAG, "库未加载，跳过释放");
            return;
        }
        
        if (!modelInitialized) {
            // android.util.Log.w(TAG, "模型未初始化，跳过释放");
            return;
        }
        
        try {
            // android.util.Log.i(TAG, "开始释放人脸模型资源...");
            releaseFaceModel();
            modelInitialized = false; // 释放后标记为未初始化
            // android.util.Log.i(TAG, "✅ 人脸模型资源释放成功");
        } catch (UnsatisfiedLinkError e) {
            android.util.Log.e(TAG, "❌ JNI释放方法未找到: " + e.getMessage());
        } catch (Exception e) {
            android.util.Log.e(TAG, "❌ 释放人脸模型异常: " + e.getMessage());
        }
    }
    
    /**
     * 检查模型是否已初始化
     */
    public boolean isModelInitialized() {
        return modelInitialized;
    }
} 