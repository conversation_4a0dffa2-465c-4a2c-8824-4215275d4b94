#include <jni.h>
#include <string>
#include <android/log.h>
#include <chrono>
#include <cstring>
#include "face_emotion_interface.h"

#define LOG_TAG "FaceEmotionSDK_JNI"
// #define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
// #define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// 注释掉的日志宏定义 - 只保留错误和警告日志
#define LOGI(...) do {} while(0)
#define LOGD(...) do {} while(0)

// 声明外部函数 - 使用头文件中的名称
extern "C" {
    JNIEXPORT jstring JNICALL Java_com_my_emotionsdkdemo_face_FaceEmotionInterface_initFaceEmotionModel(JNIEnv *, jobject, jstring, jstring);
    JNIEXPORT void JNICALL Java_com_my_emotionsdkdemo_face_FaceEmotionInterface_releaseFaceModel(JNIEnv *, jobject);
    JNIEXPORT jobjectArray JNICALL Java_com_my_emotionsdkdemo_face_FaceEmotionInterface_detectFace(JNIEnv *, jobject, jobject);
}

/**
 * 高效的ARGB到RGB转换函数 - 优化版
 * @param argb_data 输入的ARGB数据指针
 * @param rgb_data 输出的RGB数据指针
 * @param pixel_count 像素总数
 */
void convertARGBtoRGB(const uint8_t* argb_data, uint8_t* rgb_data, int pixel_count) {
    if (argb_data == nullptr || rgb_data == nullptr || pixel_count <= 0) {
        LOGE("❌ convertARGBtoRGB: 无效参数");
        return;
    }
    
    const uint32_t* argb_pixels = reinterpret_cast<const uint32_t*>(argb_data);
    uint8_t* rgb_ptr = rgb_data;
    
    // 批量处理优化 - 每次处理8个像素
    const int batch_size = 8;
    const int batch_count = pixel_count / batch_size;
    const int remaining = pixel_count % batch_size;
    
    // 批量处理主循环
    for (int batch = 0; batch < batch_count; batch++) {
        const uint32_t* batch_start = argb_pixels + batch * batch_size;
        
        // 展开循环 - 减少循环开销
        for (int i = 0; i < batch_size; i++) {
            uint32_t pixel = batch_start[i];
            *rgb_ptr++ = (pixel >> 16) & 0xFF; // R
            *rgb_ptr++ = (pixel >> 8) & 0xFF;  // G
            *rgb_ptr++ = pixel & 0xFF;         // B
        }
    }
    
    // 处理剩余像素
    const uint32_t* remaining_start = argb_pixels + batch_count * batch_size;
    for (int i = 0; i < remaining; i++) {
        uint32_t pixel = remaining_start[i];
        *rgb_ptr++ = (pixel >> 16) & 0xFF; // R
        *rgb_ptr++ = (pixel >> 8) & 0xFF;  // G
        *rgb_ptr++ = pixel & 0xFF;         // B
    }
    
    LOGD("✅ ARGB到RGB转换完成: %d 像素, %d 批次 + %d 剩余", 
         pixel_count, batch_count, remaining);
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_emotion_face_sdk_FaceEmotionInterface_initRknnModel(
    JNIEnv *env,
    jobject thiz,
    jstring faceDetectionModelPath,
    jstring emotionDetectionModelPath
) {
    LOGI("=== 开始初始化人脸情感RKNN模型 ===");
    LOGD("JNI函数: Java_com_emotion_face_sdk_FaceEmotionInterface_initRknnModel 被调用");
    
    // 检查参数是否为空
    if (faceDetectionModelPath == nullptr) {
        LOGE("❌ faceDetectionModelPath 参数为空");
        return env->NewStringUTF("Error: faceDetectionModelPath is null");
    }
    if (emotionDetectionModelPath == nullptr) {
        LOGE("❌ emotionDetectionModelPath 参数为空");
        return env->NewStringUTF("Error: emotionDetectionModelPath is null");
    }
    
    // 获取模型路径字符串用于日志
    const char *face_path = env->GetStringUTFChars(faceDetectionModelPath, 0);
    const char *emotion_path = env->GetStringUTFChars(emotionDetectionModelPath, 0);
    
    LOGI("模型初始化参数:");
    LOGI("1. 人脸检测模型: %s", face_path);
    LOGI("2. 情感识别模型: %s", emotion_path);
    
    // 释放字符串资源
    env->ReleaseStringUTFChars(faceDetectionModelPath, face_path);
    env->ReleaseStringUTFChars(emotionDetectionModelPath, emotion_path);
    
    // 直接调用外部函数进行初始化
    LOGI("准备调用外部函数: Java_com_my_emotionsdkdemo_face_FaceEmotionInterface_initFaceEmotionModel");
    
    jstring result = nullptr;
    try {
        result = Java_com_my_emotionsdkdemo_face_FaceEmotionInterface_initFaceEmotionModel(env, thiz, faceDetectionModelPath, emotionDetectionModelPath);
        LOGI("✅ 外部函数调用完成，结果指针: %p", result);
        
        if (result != nullptr) {
            const char* result_str = env->GetStringUTFChars(result, 0);
            LOGI("人脸模型初始化结果: %s", result_str);
            env->ReleaseStringUTFChars(result, result_str);
        } else {
            LOGE("❌ 外部函数返回null");
        }
    } catch (...) {
        LOGE("❌ 调用外部函数时发生异常");
        return env->NewStringUTF("Error: Exception during external function call");
    }
    
    LOGI("=== 人脸情感模型初始化完成 ===");
    return result;
}

extern "C" JNIEXPORT jobjectArray JNICALL
Java_com_emotion_face_sdk_FaceEmotionInterface_detectFace(
    JNIEnv *env,
    jobject thiz,
    jobject picBuffer,
    jobject outputRgbBuffer
) {
    LOGI("=== JNI人脸情感检测开始 ===");
    
    // 记录开始时间
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 检查参数是否为空
    if (picBuffer == nullptr) {
        LOGE("❌ picBuffer 参数为空");
        return nullptr;
    }
    
    // 检查ByteBuffer是否为Direct类型
    void* buffer_address = env->GetDirectBufferAddress(picBuffer);
    if (buffer_address == nullptr) {
        LOGE("❌ ByteBuffer不是Direct类型或获取地址失败");
        return nullptr;
    }
    
    jlong buffer_capacity = env->GetDirectBufferCapacity(picBuffer);
    LOGI("✅ ByteBuffer验证通过:");
    LOGI("  - 地址: %p", buffer_address);
    LOGI("  - 容量: %ld 字节", buffer_capacity);
    LOGI("  - 期望容量: %d 字节 (640*640*3 RGB)", 640*640*3);
    
    if (buffer_capacity != 640*640*3) {
        LOGE("❌ ByteBuffer容量不正确: %ld, 期望: %d", buffer_capacity, 640*640*3);
        return nullptr;
    }
    
    // 从outputRgbBuffer参数获取RGB缓冲区
    uint8_t* rgb_buffer = static_cast<uint8_t*>(env->GetDirectBufferAddress(outputRgbBuffer));
    if (rgb_buffer == nullptr) {
        LOGE("❌ outputRgbBuffer 无效");
        return nullptr;
    }
    
    const int rgb_buffer_size = 640 * 640 * 3;
    
    // 直接复制Java层已经转换好的RGB数据（避免重复转换）
    LOGI("🔄 复制Java层提供的RGB数据...");
    memcpy(rgb_buffer, buffer_address, rgb_buffer_size);
    LOGI("✅ RGB数据复制完成，大小: %d 字节", rgb_buffer_size);
    
    // 创建RGB格式的ByteBuffer传递给底层检测函数
    jobject rgb_byte_buffer = env->NewDirectByteBuffer(rgb_buffer, rgb_buffer_size);
    if (rgb_byte_buffer == nullptr) {
        LOGE("❌ 创建RGB ByteBuffer失败");
        return nullptr;
    }
    
    // 调用外部函数进行人脸检测
    LOGI("🔄 调用底层RKNN检测引擎...");
    
    // 记录模型推理开始时间
    auto inference_start = std::chrono::high_resolution_clock::now();
    
    jobjectArray result = nullptr;
    try {
        result = Java_com_my_emotionsdkdemo_face_FaceEmotionInterface_detectFace(env, thiz, rgb_byte_buffer);
        
        // 记录模型推理结束时间
        auto inference_end = std::chrono::high_resolution_clock::now();
        auto inference_duration = std::chrono::duration_cast<std::chrono::milliseconds>(inference_end - inference_start);
        
        LOGI("✅ 底层RKNN检测引擎完成，推理耗时: %lld ms", inference_duration.count());
        
        if (result == nullptr) {
            LOGE("❌ 底层RKNN检测引擎返回null");
            return nullptr;
        }
        
        // === JNI层直接分析和打印检测结果 ===
        jsize arrayLength = env->GetArrayLength(result);
        LOGI("🔍 JNI检测结果分析:");
        LOGI("  - 检测到人脸数量: %d", arrayLength);
        
        if (arrayLength > 0) {
            // 逐个分析每个检测结果
            for (int i = 0; i < arrayLength; i++) {
                jobject faceBoxObj = env->GetObjectArrayElement(result, i);
                
                if (faceBoxObj == nullptr) {
                    LOGE("  - FaceBox[%d]: ❌ null对象", i);
                    continue;
                }
                
                // 获取FaceBox类
                jclass faceBoxClass = env->GetObjectClass(faceBoxObj);
                
                // 获取字段ID
                jfieldID x1Field = env->GetFieldID(faceBoxClass, "x1", "I");
                jfieldID y1Field = env->GetFieldID(faceBoxClass, "y1", "I");
                jfieldID x2Field = env->GetFieldID(faceBoxClass, "x2", "I");
                jfieldID y2Field = env->GetFieldID(faceBoxClass, "y2", "I");
                jfieldID scoreField = env->GetFieldID(faceBoxClass, "score", "F");
                jfieldID emotionField = env->GetFieldID(faceBoxClass, "emotion", "Ljava/lang/String;");
                
                // 获取字段值
                jint x1 = env->GetIntField(faceBoxObj, x1Field);
                jint y1 = env->GetIntField(faceBoxObj, y1Field);
                jint x2 = env->GetIntField(faceBoxObj, x2Field);
                jint y2 = env->GetIntField(faceBoxObj, y2Field);
                jfloat score = env->GetFloatField(faceBoxObj, scoreField);
                jstring emotionStr = (jstring)env->GetObjectField(faceBoxObj, emotionField);
                
                // 获取emotion字符串
                const char* emotion = nullptr;
                if (emotionStr != nullptr) {
                    emotion = env->GetStringUTFChars(emotionStr, 0);
                }
                
                // JNI层详细打印结果
                LOGI("  - FaceBox[%d] 详细信息:", i);
                LOGI("    坐标: (%d,%d) -> (%d,%d)", x1, y1, x2, y2);
                LOGI("    置信度: %.3f", score);
                LOGI("    情感: %s", emotion ? emotion : "null");
                
                // 数据有效性检查
                bool coordsValid = (x1 >= 0 && y1 >= 0 && x2 > x1 && y2 > y1 && 
                                   x1 < 640 && y1 < 640 && x2 <= 640 && y2 <= 640);
                bool scoreValid = (score >= 0.0f && score <= 1.0f);
                bool emotionValid = (emotion != nullptr && strlen(emotion) > 0);
                
                LOGI("    有效性: 坐标=%s, 分数=%s, 情感=%s", 
                     coordsValid ? "✅" : "❌", 
                     scoreValid ? "✅" : "❌", 
                     emotionValid ? "✅" : "❌");
                
                if (coordsValid && scoreValid && emotionValid) {
                    LOGI("    ✅ FaceBox[%d] 完全有效", i);
                } else {
                    LOGW("    ❌ FaceBox[%d] 存在无效字段", i);
                }
                
                // 释放字符串
                if (emotion && emotionStr) {
                    env->ReleaseStringUTFChars(emotionStr, emotion);
                }
                
                // 清理本地引用
                env->DeleteLocalRef(faceBoxObj);
                env->DeleteLocalRef(faceBoxClass);
                if (emotionStr != nullptr) {
                    env->DeleteLocalRef(emotionStr);
                }
            }
        } else {
            LOGI("  - 未检测到人脸");
        }
        
        // 记录总耗时
        auto end_time = std::chrono::high_resolution_clock::now();
        auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        LOGI("✅ JNI处理完成: 总耗时=%lld ms, 返回%d个结果", total_duration.count(), arrayLength);
        LOGI("=== JNI人脸情感检测结束 ===");
        
        return result;
        
    } catch (...) {
        LOGE("❌ JNI层检测异常");
        return nullptr;
    }
}

extern "C" JNIEXPORT jint JNICALL
Java_com_emotion_face_sdk_FaceEmotionInterface_detectFaceOptimized(
    JNIEnv *env,
    jobject thiz,
    jobject picBuffer,
    jobject resultList,
    jobject outputRgbBuffer
) {
    LOGI("=== JNI优化版人脸情感检测开始 ===");
    
    // 记录开始时间
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 检查参数是否为空
    if (picBuffer == nullptr) {
        LOGE("❌ picBuffer 参数为空");
        return -1;
    }
    
    if (resultList == nullptr) {
        LOGE("❌ resultList 参数为空");
        return -1;
    }
    
    // 检查ByteBuffer是否为Direct类型
    void* buffer_address = env->GetDirectBufferAddress(picBuffer);
    if (buffer_address == nullptr) {
        LOGE("❌ ByteBuffer不是Direct类型或获取地址失败");
        return -1;
    }
    
    jlong buffer_capacity = env->GetDirectBufferCapacity(picBuffer);
    LOGI("✅ ByteBuffer验证通过:");
    LOGI("  - 地址: %p", buffer_address);
    LOGI("  - 容量: %ld 字节", buffer_capacity);
    LOGI("  - 期望容量: %d 字节 (640*640*3 RGB)", 640*640*3);
    
    if (buffer_capacity != 640*640*3) {
        LOGE("❌ ByteBuffer容量不正确: %ld, 期望: %d", buffer_capacity, 640*640*3);
        return -1;
    }
    
    // 从outputRgbBuffer参数获取RGB缓冲区
    uint8_t* rgb_buffer = static_cast<uint8_t*>(env->GetDirectBufferAddress(outputRgbBuffer));
    if (rgb_buffer == nullptr) {
        LOGE("❌ outputRgbBuffer 无效");
        return -1;
    }
    
    const int rgb_buffer_size = 640 * 640 * 3;
    
    // 直接复制Java层已经转换好的RGB数据（避免重复转换）
    LOGI("复制Java层提供的RGB数据...");
    memcpy(rgb_buffer, buffer_address, rgb_buffer_size);
    LOGI("✅ RGB数据复制完成，大小: %d 字节", rgb_buffer_size);
    
    // 创建RGB格式的ByteBuffer传递给底层检测函数
    jobject rgb_byte_buffer = env->NewDirectByteBuffer(rgb_buffer, rgb_buffer_size);
    if (rgb_byte_buffer == nullptr) {
        LOGE("❌ 创建RGB ByteBuffer失败");
        return -1;
    }
    
    // 调用外部函数进行人脸检测
    LOGI("调用底层RKNN检测引擎...");
    
    // 记录模型推理开始时间
    auto inference_start = std::chrono::high_resolution_clock::now();
    
    jobjectArray temp_result = nullptr;
    int face_count = 0;
    jclass listClass = nullptr;  // 在try块外声明，避免作用域问题
    
    try {
        temp_result = Java_com_my_emotionsdkdemo_face_FaceEmotionInterface_detectFace(env, thiz, rgb_byte_buffer);
        
        // 记录模型推理结束时间
        auto inference_end = std::chrono::high_resolution_clock::now();
        auto inference_duration = std::chrono::duration_cast<std::chrono::milliseconds>(inference_end - inference_start);
        
        LOGI("✅ 底层RKNN检测引擎完成，推理耗时: %lld ms", inference_duration.count());
        
        if (temp_result == nullptr) {
            LOGE("❌ 底层RKNN检测引擎返回null");
            return 0;
        }
        
        // 获取检测结果数量
        jsize arrayLength = env->GetArrayLength(temp_result);
        face_count = arrayLength;
        
        LOGI("检测结果: %d 个人脸", face_count);
        
        // 获取List类和相关方法ID
        listClass = env->GetObjectClass(resultList);
        if (listClass == nullptr) {
            LOGE("❌ 无法获取List类");
            return -1;
        }
        
        jmethodID clearMethod = env->GetMethodID(listClass, "clear", "()V");
        jmethodID addMethod = env->GetMethodID(listClass, "add", "(Ljava/lang/Object;)Z");
        
        if (clearMethod == nullptr || addMethod == nullptr) {
            LOGE("❌ 无法获取List的clear或add方法");
            env->DeleteLocalRef(listClass);
            listClass = nullptr;
            return -1;
        }
        
        // 清空原有结果
        env->CallVoidMethod(resultList, clearMethod);
        LOGI("✅ 已清空原有结果List");
        
        // 将检测结果添加到List中
        if (face_count > 0) {
            LOGI("开始填充结果到List...");
            
            for (int i = 0; i < face_count; i++) {
                jobject faceBoxObj = env->GetObjectArrayElement(temp_result, i);
                
                if (faceBoxObj != nullptr) {
                    // 将FaceBox对象添加到List中
                    jboolean addResult = env->CallBooleanMethod(resultList, addMethod, faceBoxObj);
                    
                    if (addResult) {
                        LOGD("✅ FaceBox[%d] 已添加到List", i);
                    } else {
                        LOGW("⚠️ FaceBox[%d] 添加到List失败", i);
                    }
                    
                    // 清理本地引用
                    env->DeleteLocalRef(faceBoxObj);
                } else {
                    LOGW("⚠️ FaceBox[%d] 为null，跳过", i);
                }
            }
            
            LOGI("✅ 所有结果已填充到List，共 %d 个", face_count);
        }
        
        // 清理临时数组引用
        env->DeleteLocalRef(temp_result);
        
        // 在try块中释放listClass引用
        if (listClass != nullptr) {
            env->DeleteLocalRef(listClass);
            listClass = nullptr;
        }
        
        // 记录总耗时
        auto end_time = std::chrono::high_resolution_clock::now();
        auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        LOGI("✅ JNI优化版处理完成: 总耗时=%lld ms, 返回%d个结果", total_duration.count(), face_count);
        
    } catch (...) {
        LOGE("❌ JNI优化版检测异常");
        face_count = -1;
        
        // 异常情况下也要释放局部引用
        if (listClass != nullptr) {
            env->DeleteLocalRef(listClass);
            listClass = nullptr;
        }
    }
    
    // 最终安全检查 - 防止任何遗漏的引用泄漏
    if (listClass != nullptr) {
        env->DeleteLocalRef(listClass);
        LOGW("⚠️ listClass在最终清理中被释放 - 这不应该发生");
    }
    
    LOGI("=== JNI优化版人脸情感检测结束 ===");
    return face_count;
}

extern "C" JNIEXPORT void JNICALL
Java_com_emotion_face_sdk_FaceEmotionInterface_releaseFaceModel(
    JNIEnv *env,
    jobject thiz
) {
    LOGI("=== 开始释放人脸情感模型资源 ===");
    LOGD("JNI函数: Java_com_emotion_face_sdk_FaceEmotionInterface_releaseFaceModel 被调用");
    
    try {
        Java_com_my_emotionsdkdemo_face_FaceEmotionInterface_releaseFaceModel(env, thiz);
        LOGI("✅ 人脸情感模型资源释放完成");
    } catch (...) {
        LOGE("❌ 释放人脸情感模型资源时发生异常");
    }
    
    LOGI("=== 人脸情感模型资源释放完成 ===");
}