# Face Emotion Detection Module CMakeLists.txt
cmake_minimum_required(VERSION 3.18.1)

# 设置项目名称
project("face_emotion_detection")

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17 -fPIC -O2")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC -O2")

# 查找必需的库
find_library(log-lib log)
find_library(android-lib android)

# 添加头文件目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# 设置jniLibs目录路径
set(JNI_LIBS_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../jniLibs/${ANDROID_ABI})

# 验证预编译库是否存在
message(STATUS "检查预编译库:")
message(STATUS "  - JNI_LIBS_DIR: ${JNI_LIBS_DIR}")
message(STATUS "  - ANDROID_ABI: ${ANDROID_ABI}")

# 添加预编译的库
add_library(rknnrt SHARED IMPORTED)
set_target_properties(rknnrt PROPERTIES IMPORTED_LOCATION ${JNI_LIBS_DIR}/librknnrt.so)

add_library(rga SHARED IMPORTED)
set_target_properties(rga PROPERTIES IMPORTED_LOCATION ${JNI_LIBS_DIR}/librga.so)

add_library(rknn_emotion_detection_demo SHARED IMPORTED)
set_target_properties(rknn_emotion_detection_demo PROPERTIES IMPORTED_LOCATION ${JNI_LIBS_DIR}/librknn_emotion_detection_demo.so)

# 验证库文件是否存在
if(EXISTS ${JNI_LIBS_DIR}/librknnrt.so)
    message(STATUS "  ✅ librknnrt.so found")
else()
    message(WARNING "  ❌ librknnrt.so NOT found")
endif()

if(EXISTS ${JNI_LIBS_DIR}/librga.so)
    message(STATUS "  ✅ librga.so found")
else()
    message(WARNING "  ❌ librga.so NOT found")
endif()

if(EXISTS ${JNI_LIBS_DIR}/librknn_emotion_detection_demo.so)
    message(STATUS "  ✅ librknn_emotion_detection_demo.so found")
else()
    message(WARNING "  ❌ librknn_emotion_detection_demo.so NOT found")
endif()

# 设置源文件
set(FACE_EMOTION_SOURCES
    face_emotion_interface.cpp
    face_emotion_interface.h
)

# 创建共享库
add_library(
    face_emotion_detection
    SHARED
    ${FACE_EMOTION_SOURCES}
)

# 链接库
target_link_libraries(
    face_emotion_detection
    ${log-lib}
    ${android-lib}
    rknnrt
    rga
    rknn_emotion_detection_demo
)

# 设置输出目录
set_target_properties(
    face_emotion_detection PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
)

# 编译时输出信息
message(STATUS "Face Emotion Detection SDK Configuration:")
message(STATUS "  - Project: ${PROJECT_NAME}")
message(STATUS "  - CMAKE_SYSTEM_NAME: ${CMAKE_SYSTEM_NAME}")
message(STATUS "  - CMAKE_SYSTEM_PROCESSOR: ${CMAKE_SYSTEM_PROCESSOR}")
message(STATUS "  - CMAKE_BUILD_TYPE: ${CMAKE_BUILD_TYPE}")
message(STATUS "  - Output Library: libface_emotion_detection.so") 