/* Face Emotion Detection Module JNI Interface */
#include <jni.h>

#ifndef _Included_com_my_emotionsdkdemo_face_FaceEmotionInterface
#define _Included_com_my_emotionsdkdemo_face_FaceEmotionInterface
#ifdef __cplusplus
extern "C" {
#endif

/*
 * Class:     com_my_emotionsdkdemo_face_FaceEmotionInterface
 * Method:    initFaceEmotionModel
 * Signature: (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
 * Description: 初始化人脸检测和情感识别模型
 * Parameters:
 *   - face_detection_model_path: 人脸检测模型路径
 *   - face_emotion_model_path: 人脸情感识别模型路径
 */
JNIEXPORT jstring JNICALL Java_com_my_emotionsdkdemo_face_FaceEmotionInterface_initFaceEmotionModel
        (JNIEnv *, jobject, jstring, jstring);

/*
 * Class:     com_my_emotionsdkdemo_face_FaceEmotionInterface
 * Method:    detectFace
 * Signature: (Ljava/nio/ByteBuffer;)[Lcom/my/emotionsdkdemo/FaceBox;
 * Description: 检测人脸并识别情感
 * Parameters:
 *   - pic_buffer: 640x640x3的图像数据ByteBuffer
 * Returns: FaceBox数组，包含人脸位置和情感结果
 */
JNIEXPORT jobjectArray JNICALL Java_com_my_emotionsdkdemo_face_FaceEmotionInterface_detectFace
        (JNIEnv *, jobject, jobject);

/*
 * Class:     com_my_emotionsdkdemo_face_FaceEmotionInterface
 * Method:    releaseFaceModel
 * Signature: ()V
 * Description: 释放人脸检测模块资源
 */
JNIEXPORT void JNICALL Java_com_my_emotionsdkdemo_face_FaceEmotionInterface_releaseFaceModel
        (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif