<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2022 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<animated-selector xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools"
  android:width="@dimen/mtrl_switch_thumb_size"
  android:height="@dimen/mtrl_switch_thumb_size"
  tools:ignore="NewApi">

  <item
    android:id="@+id/pressed"
    android:drawable="@drawable/mtrl_switch_thumb_pressed"
    android:state_pressed="true" />

  <item
    android:id="@+id/checked"
    android:drawable="@drawable/mtrl_switch_thumb_checked"
    android:state_checked="true" />

  <item
    android:id="@+id/with_icon"
    android:drawable="@drawable/mtrl_switch_thumb_checked"
    app:state_with_icon="true" />

  <item
    android:id="@+id/unchecked"
    android:drawable="@drawable/mtrl_switch_thumb_unchecked" />

  <transition
    android:fromId="@+id/pressed"
    android:toId="@+id/checked"
    android:drawable="@drawable/mtrl_switch_thumb_pressed_checked" />

  <transition
    android:fromId="@+id/pressed"
    android:toId="@+id/with_icon"
    android:drawable="@drawable/mtrl_switch_thumb_pressed_checked" />

  <transition
    android:fromId="@+id/pressed"
    android:toId="@+id/unchecked"
    android:drawable="@drawable/mtrl_switch_thumb_pressed_unchecked" />

  <transition
    android:fromId="@+id/checked"
    android:toId="@+id/pressed"
    android:drawable="@drawable/mtrl_switch_thumb_checked_pressed" />

  <transition
    android:fromId="@+id/checked"
    android:toId="@+id/unchecked"
    android:drawable="@drawable/mtrl_switch_thumb_checked_unchecked" />

  <transition
    android:fromId="@+id/with_icon"
    android:toId="@+id/pressed"
    android:drawable="@drawable/mtrl_switch_thumb_checked_pressed" />

  <transition
    android:fromId="@+id/unchecked"
    android:toId="@+id/pressed"
    android:drawable="@drawable/mtrl_switch_thumb_unchecked_pressed" />

  <transition
    android:fromId="@+id/unchecked"
    android:toId="@+id/checked"
    android:drawable="@drawable/mtrl_switch_thumb_unchecked_checked" />

</animated-selector>
