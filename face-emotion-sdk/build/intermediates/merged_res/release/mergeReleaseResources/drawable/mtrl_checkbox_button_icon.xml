<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2022 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
-->
<animated-selector xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  tools:ignore="NewApi">
  <item
    android:id="@+id/checked"
    android:drawable="@drawable/mtrl_ic_check_mark"
    android:state_checked="true"/>
  <item
    android:id="@+id/indeterminate"
    android:drawable="@drawable/mtrl_ic_indeterminate"
    app:state_indeterminate="true"/>
  <item
    android:id="@+id/unchecked"
    android:drawable="@android:color/transparent"
    android:state_checked="false"/>

  <transition
    android:fromId="@+id/checked"
    android:toId="@+id/unchecked"
    android:drawable="@drawable/mtrl_checkbox_button_icon_checked_unchecked" />
  <transition
    android:fromId="@+id/unchecked"
    android:toId="@+id/checked"
    android:drawable="@drawable/mtrl_checkbox_button_icon_unchecked_checked" />

  <transition
    android:fromId="@+id/indeterminate"
    android:toId="@+id/unchecked"
    android:drawable="@drawable/mtrl_checkbox_button_icon_indeterminate_unchecked" />
  <transition
    android:fromId="@+id/unchecked"
    android:toId="@+id/indeterminate"
    android:drawable="@drawable/mtrl_checkbox_button_icon_unchecked_indeterminate" />

  <transition
    android:fromId="@+id/indeterminate"
    android:toId="@+id/checked"
    android:drawable="@drawable/mtrl_checkbox_button_icon_indeterminate_checked" />
  <transition
    android:fromId="@+id/checked"
    android:toId="@+id/indeterminate"
    android:drawable="@drawable/mtrl_checkbox_button_icon_checked_indeterminate" />
</animated-selector>
