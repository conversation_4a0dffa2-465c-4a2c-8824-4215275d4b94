plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.my.emotionsdkdemo'
    compileSdk 35

    defaultConfig {
        applicationId "com.my.emotionsdkdemo"
        minSdk 25
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    buildFeatures {
        viewBinding true
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.constraintlayout
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core

    // 模块化SDK依赖
    implementation project(':face-emotion-sdk')
    implementation project(':voice-emotion-sdk')

    implementation 'com.github.gkonovalov.android-vad:silero:2.0.10'



}