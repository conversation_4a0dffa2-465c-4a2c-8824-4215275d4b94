package com.my.emotionsdkdemo.utils;

import android.content.Context;
import android.util.Log;
import org.json.JSONArray;
import org.json.JSONException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * JSON配置文件加载工具类
 */
public class JsonConfigLoader {
    private static final String TAG = "JsonConfigLoader";

    /**
     * 从assets目录加载JSON数组
     * @param context 上下文
     * @param assetPath assets中的文件路径
     * @return JSONArray对象，失败时返回null
     */
    public static JSONArray loadJsonArrayFromAssets(Context context, String assetPath) {
        try {
            // 从assets读取JSON文件
            InputStream inputStream = context.getAssets().open(assetPath);
            byte[] buffer = new byte[inputStream.available()];
            inputStream.read(buffer);
            inputStream.close();
            
            // 转换为字符串
            String jsonString = new String(buffer, StandardCharsets.UTF_8);
            
            // 解析为JSONArray
            JSONArray jsonArray = new JSONArray(jsonString);
            Log.i(TAG, "成功加载配置文件: " + assetPath + ", 数组长度: " + jsonArray.length());
            return jsonArray;
            
        } catch (IOException e) {
            Log.e(TAG, "读取配置文件失败: " + assetPath, e);
            return null;
        } catch (JSONException e) {
            Log.e(TAG, "解析JSON失败: " + assetPath, e);
            return null;
        }
    }
} 