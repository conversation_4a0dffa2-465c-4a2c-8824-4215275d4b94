package com.my.emotionsdkdemo.fusion;

import android.util.Log;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 情感融合管理器
 * 功能：
 * 1. 每秒融合一次，检查是否有批次数据
 * 2. 先计算众数情感
 * 3. 再检查人声状态
 * 4. 无人声时执行最终融合
 */
public class EmotionFusionManager {
    private static final String TAG = "EmotionFusionManager";
    
    // 融合周期：每秒一次
    private static final long FUSION_INTERVAL_MS = 1000;
    
    // 数据收集容器
    private final List<String> faceEmotionsBuffer = new ArrayList<>();
    private final List<String> voiceEmotionsBuffer = new ArrayList<>();
    private final Object bufferLock = new Object();
    
    // 人声活动状态管理
    private volatile boolean currentVoiceActivity = false;
    private volatile boolean hasFaceDetection = false;
    
    // 定时器和线程管理
    private ScheduledExecutorService scheduler;
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    
    // 统计信息
    private final AtomicLong totalFusions = new AtomicLong(0);
    private final AtomicLong successfulFusions = new AtomicLong(0);
    private final AtomicLong voiceOnlyFusions = new AtomicLong(0);
    private final AtomicLong faceOnlyFusions = new AtomicLong(0);
    private final AtomicLong failedFusions = new AtomicLong(0);
    private final AtomicLong skippedDueToVoice = new AtomicLong(0);
    private final AtomicLong skippedDueToNoData = new AtomicLong(0);
    
    // 回调接口
    private FusionCallback callback;
    
    // 情感映射处理器
    private EmotionMappingProcessor mappingProcessor;
    
    /**
     * 融合结果回调接口
     */
    public interface FusionCallback {
        /**
         * 融合成功回调
         * @param fusedEmotion 融合后的情感结果
         * @param hasVoice 是否有人声
         * @param hasFace 是否有人脸
         * @param faceCount 本次融合的人脸情感数量
         * @param voiceCount 本次融合的语音情感数量
         * @param confidence 融合置信度 (0.0-1.0)
         * @param completeResult 完整的映射处理结果（包含反馈类别）
         */
        void onFusionResult(String fusedEmotion, boolean hasVoice, boolean hasFace, 
                           int faceCount, int voiceCount, double confidence, 
                           EmotionMappingProcessor.CompleteEmotionResult completeResult);
        
        /**
         * 融合跳过回调
         * @param reason 跳过原因
         * @param hasVoice 是否有人声
         * @param hasFace 是否有人脸
         */
        void onFusionSkipped(String reason, boolean hasVoice, boolean hasFace);
        
        /**
         * 性能统计回调
         * @param totalFusions 总融合次数
         * @param successfulFusions 成功融合次数
         * @param successRate 成功率
         */
        void onPerformanceUpdate(long totalFusions, long successfulFusions, double successRate);
    }
    
    /**
     * 构造函数
     */
    public EmotionFusionManager(android.content.Context context) {
        // 初始化情感映射处理器
        this.mappingProcessor = new EmotionMappingProcessor(context);
        Log.i(TAG, "情感融合管理器创建完成，包含映射处理器");
        Log.i(TAG, "新融合逻辑: 每秒定时融合，先计算众数，再检查人声，无人声时执行融合");
    }
    
    /**
     * 设置回调接口
     */
    public void setCallback(FusionCallback callback) {
        this.callback = callback;
    }
    
    /**
     * 启动融合管理器
     */
    public boolean startFusion() {
        if (isRunning.get()) {
            Log.w(TAG, "融合管理器已在运行中");
            return true;
        }
        
        Log.i(TAG, "启动情感融合管理器 - 每秒定时融合模式");
        
        // 创建定时器
        scheduler = Executors.newScheduledThreadPool(1);
        
        // 重置状态
        synchronized (bufferLock) {
            faceEmotionsBuffer.clear();
            voiceEmotionsBuffer.clear();
            currentVoiceActivity = false;
            hasFaceDetection = false;
        }
        
        // 启动定时融合任务 - 每1秒执行一次
        scheduler.scheduleAtFixedRate(this::performFusion, 
                                    FUSION_INTERVAL_MS, 
                                    FUSION_INTERVAL_MS, 
                                    TimeUnit.MILLISECONDS);
        
        isRunning.set(true);
        Log.i(TAG, "情感融合管理器启动成功，融合周期: " + FUSION_INTERVAL_MS + "ms");
        return true;
    }
    
    /**
     * 停止融合管理器
     */
    public void stopFusion() {
        if (!isRunning.getAndSet(false)) {
            Log.w(TAG, "融合管理器未在运行中");
            return;
        }
        
        Log.i(TAG, "停止情感融合管理器");
        
        // 关闭定时器
        if (scheduler != null) {
            scheduler.shutdownNow();
            scheduler = null;
        }
        
        // 清空缓冲区
        synchronized (bufferLock) {
            faceEmotionsBuffer.clear();
            voiceEmotionsBuffer.clear();
        }
        
        // 输出最终统计
        logFinalStatistics();
        
        Log.i(TAG, "情感融合管理器已停止");
    }
    
    /**
     * 添加人脸情感结果
     */
    public void addFaceEmotion(String emotion) {
        if (!isRunning.get()) {
            return;
        }
        
        if (emotion == null || emotion.trim().isEmpty()) {
            return;
        }
        
        synchronized (bufferLock) {
            faceEmotionsBuffer.add(emotion.trim());
            hasFaceDetection = true;
        }
        
        Log.v(TAG, "添加人脸情感: " + emotion + " (缓冲区大小: " + faceEmotionsBuffer.size() + ")");
    }
    
    /**
     * 添加语音情感结果
     */
    public void addVoiceEmotion(String emotion) {
        if (!isRunning.get()) {
            return;
        }
        
        if (emotion == null || emotion.trim().isEmpty()) {
            return;
        }
        
        synchronized (bufferLock) {
            voiceEmotionsBuffer.add(emotion.trim());
        }
        
        Log.v(TAG, "添加语音情感: " + emotion + " (缓冲区大小: " + voiceEmotionsBuffer.size() + ")");
    }
    
    /**
     * 更新人声活动状态
     */
    public void updateVoiceActivity(boolean hasVoice) {
        this.currentVoiceActivity = hasVoice;
        Log.v(TAG, "更新人声活动状态: " + hasVoice);
    }
    
    /**
     * 执行融合处理 - 新逻辑：先计算众数，再检查人声，无人声时融合
     */
    private void performFusion() {
        try {
            totalFusions.incrementAndGet();
            
            List<String> faceEmotions = new ArrayList<>();
            List<String> voiceEmotions = new ArrayList<>();
            boolean voiceActive;
            boolean faceActive;
            
            // 获取当前缓冲区数据并重置
            synchronized (bufferLock) {
                faceEmotions.addAll(faceEmotionsBuffer);
                voiceEmotions.addAll(voiceEmotionsBuffer);
                voiceActive = currentVoiceActivity;
                faceActive = hasFaceDetection;
                
                // 清空缓冲区为下一秒准备
                faceEmotionsBuffer.clear();
                voiceEmotionsBuffer.clear();
                hasFaceDetection = false;
                // 不重置currentVoiceActivity，因为它由VAD状态控制
            }
            
            Log.i(TAG, String.format("融合周期 #%d: 人脸=%d个, 语音=%d个, 人声活动=%s, 人脸活动=%s",
                    totalFusions.get(), faceEmotions.size(), voiceEmotions.size(), 
                    voiceActive, faceActive));
            
            // 第一步：检查是否有数据
            if (faceEmotions.isEmpty() && voiceEmotions.isEmpty()) {
                handleFusionSkipped("无批次数据", voiceActive, faceActive);
                skippedDueToNoData.incrementAndGet();
                return;
            }
            
            // 第二步：计算众数情感
            String dominantEmotion = calculateDominantEmotion(faceEmotions, voiceEmotions);
            Log.i(TAG, String.format("众数计算结果: %s", dominantEmotion));
            
            // 第三步：检查人声状态
            if (voiceActive) {
                // 有人声，跳过融合
                Log.i(TAG, "检测到人声活动，跳过本次融合");
                handleFusionSkipped("检测到人声活动", voiceActive, faceActive);
                skippedDueToVoice.incrementAndGet();
                return;
            }
            
            // 第四步：无人声，执行最终融合
            Log.i(TAG, "无人声检测，开始执行融合处理");
            
            double confidence = calculateFusionConfidence(faceEmotions, voiceEmotions, dominantEmotion);
            
            // 记录成功融合和统计
            successfulFusions.incrementAndGet();
            
            // 更新统计信息
            if (faceEmotions.isEmpty() && !voiceEmotions.isEmpty()) {
                voiceOnlyFusions.incrementAndGet();
            } else if (!faceEmotions.isEmpty() && voiceEmotions.isEmpty()) {
                faceOnlyFusions.incrementAndGet();
            }
            
            Log.i(TAG, String.format("融合成功: %s (置信度: %.2f, 人脸: %d, 语音: %d)",
                    dominantEmotion, confidence, faceEmotions.size(), voiceEmotions.size()));
            
            // 执行情感映射处理
            EmotionMappingProcessor.CompleteEmotionResult completeResult = null;
            if (mappingProcessor != null) {
                // 合并所有情感数据用于映射处理
                List<String> allEmotions = new ArrayList<>();
                allEmotions.addAll(faceEmotions);
                allEmotions.addAll(voiceEmotions);
                
                // 调用映射处理器（包含反馈类别和压力传感器生成）
                completeResult = mappingProcessor.processEmotionsWithSensors(allEmotions, false); // 无人声状态
                Log.i(TAG, "映射处理完成: " + completeResult.toString());
                Log.i(TAG, "反馈类别: " + completeResult.feedbackCategory);
                if (completeResult.pressureSensors != null) {
                    Log.i(TAG, "压力传感器数据: " + Arrays.toString(completeResult.pressureSensors));
                }
            }
            
            // 回调结果
            if (callback != null) {
                callback.onFusionResult(dominantEmotion, false, faceActive, // 无人声时才融合
                                      faceEmotions.size(), voiceEmotions.size(), confidence, completeResult);
            }
            
            // 定期输出性能统计
            if (totalFusions.get() % 5 == 0) {
                reportPerformanceStatistics();
            }
            
        } catch (Exception e) {
            Log.e(TAG, "融合处理异常", e);
            failedFusions.incrementAndGet();
        }
    }
    
    /**
     * 计算主导情感（众数）
     */
    private String calculateDominantEmotion(List<String> faceEmotions, List<String> voiceEmotions) {
        // 合并所有情感数据
        List<String> allEmotions = new ArrayList<>();
        allEmotions.addAll(faceEmotions);
        allEmotions.addAll(voiceEmotions);
        
        // 安全检查：确保至少有一个情感数据
        if (allEmotions.isEmpty()) {
            Log.w(TAG, "警告: 没有情感数据进行众数计算");
            return "neutral"; // 返回默认情感
        }
        
        // 计算众数
        Map<String, Integer> emotionCount = new HashMap<>();
        for (String emotion : allEmotions) {
            emotionCount.put(emotion, emotionCount.getOrDefault(emotion, 0) + 1);
        }
        
        // 找出出现频率最高的情感
        String mostFrequentEmotion = null;
        int maxCount = 0;
        
        for (Map.Entry<String, Integer> entry : emotionCount.entrySet()) {
            if (entry.getValue() > maxCount) {
                maxCount = entry.getValue();
                mostFrequentEmotion = entry.getKey();
            }
        }
        
        // 如果有多个情感出现频率相同，优先选择人脸情感
        if (mostFrequentEmotion == null) {
            mostFrequentEmotion = faceEmotions.isEmpty() ? 
                                 voiceEmotions.get(0) : faceEmotions.get(0);
        }
        
        Log.d(TAG, String.format("众数计算: %s (出现%d次/%d个样本)", 
                mostFrequentEmotion, maxCount, allEmotions.size()));
        
        return mostFrequentEmotion;
    }
    
    /**
     * 处理融合跳过的情况
     */
    private void handleFusionSkipped(List<String> faceEmotions, List<String> voiceEmotions, 
                                   boolean voiceActive, boolean faceActive, String reason) {
        Log.d(TAG, String.format("融合跳过: %s (人脸: %d, 语音: %d)", 
                reason, faceEmotions.size(), voiceEmotions.size()));
        
        if (callback != null) {
            callback.onFusionSkipped(reason, voiceActive, faceActive);
        }
    }
    
    /**
     * 处理融合跳过的情况 - 重载方法
     */
    private void handleFusionSkipped(String reason, boolean voiceActive, boolean faceActive) {
        Log.d(TAG, String.format("融合跳过: %s", reason));
        
        if (callback != null) {
            callback.onFusionSkipped(reason, voiceActive, faceActive);
        }
    }
    
    /**
     * 计算融合置信度
     */
    private double calculateFusionConfidence(List<String> faceEmotions, List<String> voiceEmotions, 
                                           String fusedEmotion) {
        int totalSamples = faceEmotions.size() + voiceEmotions.size();
        
        // 计算融合情感在所有样本中的占比
        int fusedCount = 0;
        for (String emotion : faceEmotions) {
            if (emotion.equals(fusedEmotion)) {
                fusedCount++;
            }
        }
        for (String emotion : voiceEmotions) {
            if (emotion.equals(fusedEmotion)) {
                fusedCount++;
            }
        }
        
        // 计算置信度
        double confidence = totalSamples > 0 ? (double) fusedCount / totalSamples : 0.0;
        
        // 额外的置信度调整：如果人脸和语音都有数据且结果一致，提高置信度
        if (!faceEmotions.isEmpty() && !voiceEmotions.isEmpty()) {
            boolean faceHasFused = faceEmotions.contains(fusedEmotion);
            boolean voiceHasFused = voiceEmotions.contains(fusedEmotion);
            if (faceHasFused && voiceHasFused) {
                confidence = Math.min(1.0, confidence * 1.2); // 提高20%
            }
        }
        
        return confidence;
    }
    
    /**
     * 输出性能统计报告
     */
    private void reportPerformanceStatistics() {
        long total = totalFusions.get();
        long successful = successfulFusions.get();
        long voiceOnly = voiceOnlyFusions.get();
        long faceOnly = faceOnlyFusions.get();
        long failed = failedFusions.get();
        long skippedVoice = skippedDueToVoice.get();
        long skippedNoData = skippedDueToNoData.get();
        
        double successRate = total > 0 ? (double) successful / total * 100 : 0;
        
        Log.i(TAG, "=== 融合性能统计 ===");
        Log.i(TAG, String.format("  总融合次数: %d", total));
        Log.i(TAG, String.format("  成功融合: %d (%.1f%%)", successful, successRate));
        Log.i(TAG, String.format("  仅语音融合: %d, 仅人脸融合: %d, 失败: %d", voiceOnly, faceOnly, failed));
        Log.i(TAG, String.format("  因人声跳过: %d, 因无数据跳过: %d", skippedVoice, skippedNoData));
        
        if (callback != null) {
            callback.onPerformanceUpdate(total, successful, successRate);
        }
    }
    
    /**
     * 输出最终统计信息
     */
    private void logFinalStatistics() {
        Log.i(TAG, "=== 最终融合统计 ===");
        reportPerformanceStatistics();
        Log.i(TAG, "每秒定时融合模式统计完成");
        Log.i(TAG, "========================");
    }
    
    /**
     * 获取运行状态
     */
    public boolean isRunning() {
        return isRunning.get();
    }
    
    /**
     * 获取缓冲区状态
     */
    public Map<String, Object> getBufferStatus() {
        Map<String, Object> status = new HashMap<>();
        synchronized (bufferLock) {
            status.put("faceEmotionsCount", faceEmotionsBuffer.size());
            status.put("voiceEmotionsCount", voiceEmotionsBuffer.size());
            status.put("currentVoiceActivity", currentVoiceActivity);
            status.put("hasFaceDetection", hasFaceDetection);
        }
        return status;
    }
    
    /**
     * 获取性能统计
     */
    public Map<String, Long> getPerformanceStats() {
        Map<String, Long> stats = new HashMap<>();
        stats.put("totalFusions", totalFusions.get());
        stats.put("successfulFusions", successfulFusions.get());
        stats.put("voiceOnlyFusions", voiceOnlyFusions.get());
        stats.put("faceOnlyFusions", faceOnlyFusions.get());
        stats.put("failedFusions", failedFusions.get());
        stats.put("skippedDueToVoice", skippedDueToVoice.get());
        stats.put("skippedDueToNoData", skippedDueToNoData.get());
        return stats;
    }
} 