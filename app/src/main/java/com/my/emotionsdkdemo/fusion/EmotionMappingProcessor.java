package com.my.emotionsdkdemo.fusion;

import android.content.Context;
import android.util.Log;
import org.json.JSONObject;
import org.json.JSONArray;
import com.my.emotionsdkdemo.utils.JsonConfigLoader;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 情感映射处理器
 * 实现情感标签的映射、权重处理和反演逻辑
 */
public class EmotionMappingProcessor {
    private static final String TAG = "EmotionMappingProcessor";
    
    private JSONArray charactorConfig;   // 情感标签映射文件 (数组)
    private JSONArray weightConfig;      // 反演权重文件 (数组)
    private JSONArray calcTableConfig;   // 宠物性格文件 (二维数组)
    
    // 基础表情代码池（每个反馈类别的基础A/B/C代码）
    private static final Map<Integer, String[]> BASE_EXPRESSION_CODES = new HashMap<>();
    
    // 基础语音代码池（每个反馈类别的基础S代码）
    private static final Map<Integer, String[]> BASE_VOICE_CODES = new HashMap<>();
    
    // 压力传感器增强表情代码（每种状态对应每个反馈类别的额外A/B/C代码）
    private static final Map<String, Map<Integer, String[]>> PRESSURE_ENHANCEMENT_EXPRESSION_CODES = new HashMap<>();
    
    // 压力传感器增强语音代码（每种状态对应每个反馈类别的额外S代码）
    private static final Map<String, Map<Integer, String[]>> PRESSURE_ENHANCEMENT_VOICE_CODES = new HashMap<>();
    
    static {
        // 初始化基础表情代码池
        BASE_EXPRESSION_CODES.put(0, new String[]{"A01", "A03", "A05", "A06", "A07", "A08", "A09", "A10"}); // 反馈类别0
        BASE_EXPRESSION_CODES.put(1, new String[]{"A04", "A14", "A11", "A12", "A09", "A10"}); // 反馈类别1
        BASE_EXPRESSION_CODES.put(2, new String[]{"A02", "A13", "A06", "A07", "A05", "A15"}); // 反馈类别2
        BASE_EXPRESSION_CODES.put(3, new String[]{"B04", "B01", "B03", "A14"}); // 反馈类别3
        BASE_EXPRESSION_CODES.put(4, new String[]{"B05", "B07", "B09"}); // 反馈类别4
        BASE_EXPRESSION_CODES.put(5, new String[]{"B07", "B02", "B08", "A14", "A15"}); // 反馈类别5
        BASE_EXPRESSION_CODES.put(6, new String[]{"C01", "C02", "C04", "C06"}); // 反馈类别6
        BASE_EXPRESSION_CODES.put(7, new String[]{"C03", "C05", "C01"}); // 反馈类别7
        BASE_EXPRESSION_CODES.put(8, new String[]{"C06", "C03", "C05"}); // 反馈类别8
        
        // 初始化基础语音代码池
        BASE_VOICE_CODES.put(0, new String[]{"S87", "S03", "S88", "S01", "S06", "S91", "S05", "S10", "S89", "S02", "S08"}); // 反馈类别0
        BASE_VOICE_CODES.put(1, new String[]{"S87", "S03", "S88", "S01", "S06", "S91", "S05", "S89", "S92", "S10", "S15", "S12", "S35", "S37", "S93"}); // 反馈类别1
        BASE_VOICE_CODES.put(2, new String[]{"S03", "S88", "S103", "S102", "S105", "S34", "S32", "S92", "S104", "S04", "S26", "S78", "S37", "S16", "S15", "S01"}); // 反馈类别2
        BASE_VOICE_CODES.put(3, new String[]{"S03", "S106", "S10", "S04", "S92", "S78", "S35", "S95", "S03", "S93", "S107", "S27", "S93", "S102", "S12", "S38"}); // 反馈类别3
        BASE_VOICE_CODES.put(4, new String[]{"S104", "S03", "S10", "S01", "S28", "S07", "S32", "S33", "S34", "S37"}); // 反馈类别4
        BASE_VOICE_CODES.put(5, new String[]{"S103", "S03", "S107", "S93", "S78", "S95", "S102", "S12", "S03", "S93", "S94", "S38", "S90", "S37", "S32", "S18"}); // 反馈类别5
        BASE_VOICE_CODES.put(6, new String[]{"S103", "S03", "S107", "S93", "S78", "S95", "S102", "S12", "S03", "S93", "S94", "S38", "S90", "S37", "S32", "S18"}); // 反馈类别6
        BASE_VOICE_CODES.put(7, new String[]{"S78", "S90", "S107", "S94", "S95", "S108", "S86", "S84"}); // 反馈类别7
        BASE_VOICE_CODES.put(8, new String[]{"S78", "S90", "S107", "S94", "S95", "S108", "S86", "S84"}); // 反馈类别8
        
        // 初始化压力传感器增强表情代码（图一的内容）
        // [1,0,0] 状态的表情增强代码
        Map<Integer, String[]> sensor100Expression = new HashMap<>();
        sensor100Expression.put(0, new String[]{"A02", "A07", "A04", "A01"}); // 适用于所有反馈类别
        PRESSURE_ENHANCEMENT_EXPRESSION_CODES.put("[1,0,0]", sensor100Expression);
        
        // [0,1,0] 状态的表情增强代码
        Map<Integer, String[]> sensor010Expression = new HashMap<>();
        sensor010Expression.put(0, new String[]{"A07", "A06", "A02", "A05"}); // 适用于所有反馈类别
        PRESSURE_ENHANCEMENT_EXPRESSION_CODES.put("[0,1,0]", sensor010Expression);
        
        // [0,0,1] 状态的表情增强代码
        Map<Integer, String[]> sensor001Expression = new HashMap<>();
        sensor001Expression.put(0, new String[]{"A09", "A05", "A02", "A10"}); // 适用于所有反馈类别
        PRESSURE_ENHANCEMENT_EXPRESSION_CODES.put("[0,0,1]", sensor001Expression);
        
        // [1,1,0] 状态的表情增强代码
        Map<Integer, String[]> sensor110Expression = new HashMap<>();
        sensor110Expression.put(0, new String[]{"A02", "A07", "A04", "A01", "A07", "A06", "A02", "A05"}); // 适用于所有反馈类别
        PRESSURE_ENHANCEMENT_EXPRESSION_CODES.put("[1,1,0]", sensor110Expression);
        
        // [1,0,1] 状态的表情增强代码
        Map<Integer, String[]> sensor101Expression = new HashMap<>();
        sensor101Expression.put(0, new String[]{"A02", "A07", "A04", "A01", "A09", "A05", "A02", "A10"}); // 适用于所有反馈类别
        PRESSURE_ENHANCEMENT_EXPRESSION_CODES.put("[1,0,1]", sensor101Expression);
        
        // [0,1,1] 状态的表情增强代码
        Map<Integer, String[]> sensor011Expression = new HashMap<>();
        sensor011Expression.put(0, new String[]{"A07", "A06", "A02", "A05", "A09", "A05", "A02", "A10"}); // 适用于所有反馈类别
        PRESSURE_ENHANCEMENT_EXPRESSION_CODES.put("[0,1,1]", sensor011Expression);
        
        // [1,1,1] 状态的表情增强代码
        Map<Integer, String[]> sensor111Expression = new HashMap<>();
        sensor111Expression.put(0, new String[]{"A02", "A07", "A04", "A01", "A07", "A06", "A02", "A05", "A09", "A05", "A02", "A10"}); // 适用于所有反馈类别
        PRESSURE_ENHANCEMENT_EXPRESSION_CODES.put("[1,1,1]", sensor111Expression);
        
        // 初始化压力传感器增强语音代码（图一的内容）
        // [1,0,0] 状态的语音增强代码
        Map<Integer, String[]> sensor100Voice = new HashMap<>();
        sensor100Voice.put(0, new String[]{"S01", "S02"}); // 适用于所有反馈类别
        PRESSURE_ENHANCEMENT_VOICE_CODES.put("[1,0,0]", sensor100Voice);
        
        // [0,1,0] 状态的语音增强代码
        Map<Integer, String[]> sensor010Voice = new HashMap<>();
        sensor010Voice.put(0, new String[]{"S03"}); // 适用于所有反馈类别
        PRESSURE_ENHANCEMENT_VOICE_CODES.put("[0,1,0]", sensor010Voice);
        
        // [0,0,1] 状态的语音增强代码
        Map<Integer, String[]> sensor001Voice = new HashMap<>();
        sensor001Voice.put(0, new String[]{"S04"}); // 适用于所有反馈类别
        PRESSURE_ENHANCEMENT_VOICE_CODES.put("[0,0,1]", sensor001Voice);
        
        // [1,1,0] 状态的语音增强代码
        Map<Integer, String[]> sensor110Voice = new HashMap<>();
        sensor110Voice.put(0, new String[]{"S01", "S02", "S03"}); // 适用于所有反馈类别
        PRESSURE_ENHANCEMENT_VOICE_CODES.put("[1,1,0]", sensor110Voice);
        
        // [1,0,1] 状态的语音增强代码
        Map<Integer, String[]> sensor101Voice = new HashMap<>();
        sensor101Voice.put(0, new String[]{"S01", "S02", "S04"}); // 适用于所有反馈类别
        PRESSURE_ENHANCEMENT_VOICE_CODES.put("[1,0,1]", sensor101Voice);
        
        // [0,1,1] 状态的语音增强代码
        Map<Integer, String[]> sensor011Voice = new HashMap<>();
        sensor011Voice.put(0, new String[]{"S03", "S04"}); // 适用于所有反馈类别
        PRESSURE_ENHANCEMENT_VOICE_CODES.put("[0,1,1]", sensor011Voice);
        
        // [1,1,1] 状态的语音增强代码
        Map<Integer, String[]> sensor111Voice = new HashMap<>();
        sensor111Voice.put(0, new String[]{"S01", "S02", "S03", "S04"}); // 适用于所有反馈类别
        PRESSURE_ENHANCEMENT_VOICE_CODES.put("[1,1,1]", sensor111Voice);
        
        // 其他压力传感器组合的映射可以继续添加...
    }

    public EmotionMappingProcessor(Context context) {
        loadConfigurations(context);
    }
    
    /**
     * 加载所有配置文件
     */
    private void loadConfigurations(Context context) {
        charactorConfig = JsonConfigLoader.loadJsonArrayFromAssets(context, "config/charactor.json");
        weightConfig = JsonConfigLoader.loadJsonArrayFromAssets(context, "config/weight.json");
        calcTableConfig = JsonConfigLoader.loadJsonArrayFromAssets(context, "config/calc_table.json");
        
        if (charactorConfig != null) {
            Log.i(TAG, "情感标签映射文件加载成功，长度: " + charactorConfig.length());
        } else {
            Log.e(TAG, "情感标签映射文件加载失败");
        }
        
        if (weightConfig != null) {
            Log.i(TAG, "反演权重文件加载成功，长度: " + weightConfig.length());
        } else {
            Log.e(TAG, "反演权重文件加载失败");
        }
        
        if (calcTableConfig != null) {
            Log.i(TAG, "宠物性格文件加载成功，行数: " + calcTableConfig.length());
        } else {
            Log.e(TAG, "宠物性格文件加载失败");
        }
    }
    
    /**
     * 处理情感数据的完整流程
     * @param emotionArray 秒内收集的情感标签数组
     * @param hasVoice 是否有人声
     * @return 处理结果
     */
    public EmotionProcessResult processEmotions(List<String> emotionArray, boolean hasVoice) {
        Log.d(TAG, "开始处理情感数据，输入: " + emotionArray.toString() + ", 有人声: " + hasVoice);
        
        // 1. 判断是否有人声
        if (!hasVoice) {
            // 无人声 → 输出为未来交互的统计标签
            String statisticalLabel = generateStatisticalLabel(emotionArray);
            Log.i(TAG, "无人声，输出统计标签: " + statisticalLabel);
            return new EmotionProcessResult(statisticalLabel, null, false);
        }
        
        // 2. 有人声，进行数组取众数
        String dominantEmotion = calculateModeEmotion(emotionArray);
        Log.d(TAG, "众数计算结果: " + dominantEmotion);
        
        // 3. 根据情感标签映射，转换为一维数组
        double[] mappedArray = mapEmotionToArray(dominantEmotion);
        if (mappedArray == null) {
            Log.e(TAG, "情感映射失败");
            return new EmotionProcessResult(dominantEmotion, null, true);
        }
        Log.d(TAG, "映射后的一维数组: " + Arrays.toString(mappedArray));
        
        // 4. 根据权重进行反演处理
        double[] finalArray = processWithWeights(mappedArray);
        Log.i(TAG, "最终反演数组: " + Arrays.toString(finalArray));
        
        return new EmotionProcessResult(dominantEmotion, finalArray, true);
    }
    
    /**
     * 生成统计标签（无人声情况）
     */
    private String generateStatisticalLabel(List<String> emotionArray) {
        if (emotionArray.isEmpty()) {
            return "neutral";
        }
        
        // 统计各情感出现频率
        Map<String, Integer> emotionCount = new HashMap<>();
        for (String emotion : emotionArray) {
            emotionCount.put(emotion, emotionCount.getOrDefault(emotion, 0) + 1);
        }
        
        // 生成统计标签格式：emotion1_count1_emotion2_count2
        StringBuilder statisticalLabel = new StringBuilder();
        for (Map.Entry<String, Integer> entry : emotionCount.entrySet()) {
            if (statisticalLabel.length() > 0) {
                statisticalLabel.append("_");
            }
            statisticalLabel.append(entry.getKey()).append("_").append(entry.getValue());
        }
        
        return statisticalLabel.toString();
    }
    
    /**
     * 计算众数情感
     */
    private String calculateModeEmotion(List<String> emotionArray) {
        if (emotionArray.isEmpty()) {
            return "neutral";
        }
        
        Map<String, Integer> emotionCount = new HashMap<>();
        for (String emotion : emotionArray) {
            emotionCount.put(emotion, emotionCount.getOrDefault(emotion, 0) + 1);
        }
        
        String modeEmotion = null;
        int maxCount = 0;
        for (Map.Entry<String, Integer> entry : emotionCount.entrySet()) {
            if (entry.getValue() > maxCount) {
                maxCount = entry.getValue();
                modeEmotion = entry.getKey();
            }
        }
        
        return modeEmotion != null ? modeEmotion : "neutral";
    }
    
    /**
     * 将情感映射为一维数组
     */
    private double[] mapEmotionToArray(String emotion) {
        if (calcTableConfig == null) {
            Log.e(TAG, "calc_table配置未加载");
            return null;
        }
        
        try {
            // 根据情感类型从calc_table.json中获取对应的行作为一维数组
            int rowIndex = getEmotionRowIndex(emotion);
            JSONArray row = calcTableConfig.getJSONArray(rowIndex);
            
            double[] result = new double[row.length()];
            for (int i = 0; i < row.length(); i++) {
                result[i] = row.getDouble(i);
            }
            
            Log.d(TAG, "情感 " + emotion + " 映射为一维数组(行" + rowIndex + "): " + Arrays.toString(result));
            return result;
            
        } catch (Exception e) {
            Log.e(TAG, "情感映射解析失败", e);
            return new double[]{0, 0, 0, 0, 0, 0, 0, 0, 0}; // 返回9个0的默认值
        }
    }

    /**
     * 根据权重进行反演处理
     */
    private double[] processWithWeights(double[] mappedArray) {
        if (weightConfig == null) {
            Log.e(TAG, "权重配置未加载");
            return mappedArray;
        }
        
        try {
            // 从weight.json数组中获取权重值 [0.5, 0.5]
            double weight1 = weightConfig.getDouble(0); // 一维数组权重
            double weight2 = weightConfig.getDouble(1); // 宠物性格文件数组权重
            
            Log.d(TAG, "权重值: 一维数组权重=" + weight1 + ", 宠物性格文件数组权重=" + weight2);
            
            // 获取宠物性格文件数组（来自charactor.json）
            double[] petCharacterArray = getPetCharacterArray();
            if (petCharacterArray == null) {
                Log.e(TAG, "无法获取宠物性格文件数组");
                return mappedArray;
            }
            
            // 确保两个数组长度一致
            int length = Math.min(mappedArray.length, petCharacterArray.length);
            double[] result = new double[length];
            
            // 计算加权平均：一维数组×权重1 + 宠物性格文件数组×权重2
            for (int i = 0; i < length; i++) {
                result[i] = mappedArray[i] * weight1 + petCharacterArray[i] * weight2;
                Log.v(TAG, "位置" + i + ": " + mappedArray[i] + "×" + weight1 + " + " + petCharacterArray[i] + "×" + weight2 + " = " + result[i]);
            }
            
            return result;
            
        } catch (Exception e) {
            Log.e(TAG, "权重处理失败", e);
            return mappedArray;
        }
    }
    
    /**
     * 获取宠物性格文件数组（来自charactor.json）
     */
    private double[] getPetCharacterArray() {
        if (charactorConfig == null) {
            Log.e(TAG, "宠物性格文件未加载");
            return null;
        }
        
        try {
            double[] result = new double[charactorConfig.length()];
            for (int i = 0; i < charactorConfig.length(); i++) {
                result[i] = charactorConfig.getDouble(i);
            }
            
            Log.d(TAG, "宠物性格文件数组: " + Arrays.toString(result));
            return result;
            
        } catch (Exception e) {
            Log.e(TAG, "获取宠物性格数组失败", e);
            return null;
        }
    }
    
    /**
     * 根据情感类型获取对应的行索引
     * 支持face模块返回的英文标签: ["Angry", "Disgusted", "Fearful", "Happy", "Neutral", "Sad", "Surprised"]
     */
    private int getEmotionRowIndex(String emotion) {
        // 映射情感类型到calc_table.json中的行索引
        switch (emotion.toLowerCase()) {
            // Face模块英文标签映射
            case "neutral":
                return 0;
            case "happy":
                return 1;
            case "angry":
                return 2;
            case "disgusted":
                return 3;
            case "sad":
                return 5;
            case "fearful":
                return 6;
            case "surprised":
                return 7;
            default:
                Log.w(TAG, "未知情感类型: " + emotion + ", 使用默认行索引0");
                return 0; // 默认返回第一行
        }
    }
    
    /**
     * 情感处理结果类
     */
    public static class EmotionProcessResult {
        public final String dominantEmotion;    // 主导情感
        public final double[] processedArray;   // 处理后的数组
        public final boolean hasVoiceProcessing; // 是否进行了有声处理
        
        public EmotionProcessResult(String dominantEmotion, double[] processedArray, boolean hasVoiceProcessing) {
            this.dominantEmotion = dominantEmotion;
            this.processedArray = processedArray;
            this.hasVoiceProcessing = hasVoiceProcessing;
        }
        
        @Override
        public String toString() {
            return "EmotionProcessResult{" +
                    "dominantEmotion='" + dominantEmotion + '\'' +
                    ", processedArray=" + Arrays.toString(processedArray) +
                    ", hasVoiceProcessing=" + hasVoiceProcessing +
                    '}';
        }
    }

    /**
     * 根据概率数组生成随机整数（反馈类别）
     * @param probabilityArray 概率数组，例如 [65,5,15,1,1,13,0,0,0]
     * @return 随机选择的索引值（反馈类别）
     */
    public int generateFeedbackCategory(double[] probabilityArray) {
        if (probabilityArray == null || probabilityArray.length == 0) {
            Log.e(TAG, "概率数组为空");
            return 0;
        }
        
        // 计算总概率
        double totalProbability = 0;
        for (double prob : probabilityArray) {
            totalProbability += prob;
        }
        
        if (totalProbability <= 0) {
            Log.e(TAG, "总概率为0或负数");
            return 0;
        }
        
        // 生成随机数 [0, totalProbability)
        double randomValue = Math.random() * totalProbability;
        
        // 根据概率分布选择索引
        double cumulativeProbability = 0;
        for (int i = 0; i < probabilityArray.length; i++) {
            cumulativeProbability += probabilityArray[i];
            if (randomValue < cumulativeProbability) {
                Log.d(TAG, "概率数组: " + Arrays.toString(probabilityArray));
                Log.d(TAG, "总概率: " + totalProbability + ", 随机值: " + randomValue);
                Log.d(TAG, "选择索引: " + i + ", 概率: " + probabilityArray[i] + " (" + 
                      String.format("%.1f%%", probabilityArray[i] / totalProbability * 100) + ")");
                return i;
            }
        }
        
        // 如果没有选中任何索引，返回最后一个非零概率的索引
        for (int i = probabilityArray.length - 1; i >= 0; i--) {
            if (probabilityArray[i] > 0) {
                Log.w(TAG, "使用备用索引: " + i);
                return i;
            }
        }
        
        return 0; // 最后的备用方案
    }

    /**
     * 生成3路压力传感器数据模拟（随机0或1）
     * @return 3路压力传感器数组，例如 [0, 1, 0]
     */
    public int[] generatePressureSensors() {
        int[] pressureSensors = new int[3];
        
        for (int i = 0; i < 3; i++) {
            // 随机生成0或1
            pressureSensors[i] = Math.random() < 0.5 ? 0 : 1;
        }
        
        Log.d(TAG, "压力传感器模拟数据: " + Arrays.toString(pressureSensors));
        return pressureSensors;
    }

    /**
     * 处理情感数据的完整流程（包含反馈类别、压力传感器和反馈代码）
     * @param emotionArray 秒内收集的情感标签数组
     * @param hasVoice 是否有人声
     * @return 完整的处理结果（包含压力传感器数据和反馈代码）
     */
    public CompleteEmotionResult processEmotionsWithSensors(List<String> emotionArray, boolean hasVoice) {
        // 1. 基础情感处理
        EmotionProcessResult basicResult = processEmotions(emotionArray, hasVoice);
        
        // 2. 如果有反馈数组，生成反馈类别
        int feedbackCategory = -1;
        if (basicResult.processedArray != null) {
            feedbackCategory = generateFeedbackCategory(basicResult.processedArray);
        }
        
        // 3. 生成3路压力传感器数据
        int[] pressureSensors = generatePressureSensors();
        
        // 4. 根据压力传感器数据和反馈类别生成反馈代码
        FeedbackCodes feedbackCodes = null;
        if (feedbackCategory != -1) {
            feedbackCodes = generateFeedbackCodes(pressureSensors, feedbackCategory);
        }
        
        return new CompleteEmotionResult(basicResult, feedbackCategory, pressureSensors, feedbackCodes);
    }

    /**
     * 完整的情感处理结果类
     */
    public static class CompleteEmotionResult {
        public final EmotionProcessResult basicResult;  // 基础处理结果
        public final int feedbackCategory;              // 反馈类别（随机生成的索引）
        public final int[] pressureSensors;             // 3路压力传感器数据
        public final FeedbackCodes feedbackCodes;       // 反馈代码
        
        public CompleteEmotionResult(EmotionProcessResult basicResult, int feedbackCategory) {
            this.basicResult = basicResult;
            this.feedbackCategory = feedbackCategory;
            this.pressureSensors = null;
            this.feedbackCodes = null;
        }
        
        public CompleteEmotionResult(EmotionProcessResult basicResult, int feedbackCategory, int[] pressureSensors) {
            this.basicResult = basicResult;
            this.feedbackCategory = feedbackCategory;
            this.pressureSensors = pressureSensors;
            this.feedbackCodes = null;
        }
        
        public CompleteEmotionResult(EmotionProcessResult basicResult, int feedbackCategory, int[] pressureSensors, FeedbackCodes feedbackCodes) {
            this.basicResult = basicResult;
            this.feedbackCategory = feedbackCategory;
            this.pressureSensors = pressureSensors;
            this.feedbackCodes = feedbackCodes;
        }
        
        @Override
        public String toString() {
            return "CompleteEmotionResult{" +
                    "dominantEmotion='" + basicResult.dominantEmotion + '\'' +
                    ", processedArray=" + Arrays.toString(basicResult.processedArray) +
                    ", hasVoiceProcessing=" + basicResult.hasVoiceProcessing +
                    ", feedbackCategory=" + feedbackCategory +
                    ", pressureSensors=" + Arrays.toString(pressureSensors) +
                    ", feedbackCodes=" + feedbackCodes +
                    '}';
        }
    }

    /**
     * 根据压力传感器数据和反馈类别生成反馈代码（增强模式）
     * @param pressureSensors 压力传感器数据 [0,1,0]
     * @param feedbackCategory 反馈类别 0-8
     * @return 反馈代码数组，包含表情反馈值和语音反馈值
     */
    public FeedbackCodes generateFeedbackCodes(int[] pressureSensors, int feedbackCategory) {
        if (pressureSensors == null || pressureSensors.length != 3) {
            Log.e(TAG, "压力传感器数据无效");
            return null;
        }
        
        if (feedbackCategory < 0 || feedbackCategory > 8) {
            Log.e(TAG, "反馈类别无效: " + feedbackCategory);
            return null;
        }
        
        // 检查是否全为0
        boolean allZero = true;
        for (int sensor : pressureSensors) {
            if (sensor != 0) {
                allZero = false;
                break;
            }
        }
        
        if (allZero) {
            Log.i(TAG, "压力传感器数据全为0，跳过反馈代码生成");
            return new FeedbackCodes(null, null);
        }
        
        // 1. 获取当前反馈类别的基础表情代码池
        String[] baseExpressionCodes = BASE_EXPRESSION_CODES.get(feedbackCategory);
        if (baseExpressionCodes == null) {
            Log.e(TAG, "未找到反馈类别 " + feedbackCategory + " 的基础表情代码");
            return null;
        }
        
        // 2. 获取当前反馈类别的语音代码池
        String[] baseVoiceCodes = BASE_VOICE_CODES.get(feedbackCategory);
        if (baseVoiceCodes == null) {
            Log.e(TAG, "未找到反馈类别 " + feedbackCategory + " 的基础语音代码");
            return null;
        }
        
        // 3. 创建增强表情代码池，先添加基础表情代码
        List<String> enhancedExpressionPool = new ArrayList<>();
        enhancedExpressionPool.addAll(Arrays.asList(baseExpressionCodes));
        
        // 4. 创建增强语音代码池，先添加基础语音代码
        List<String> enhancedVoicePool = new ArrayList<>();
        enhancedVoicePool.addAll(Arrays.asList(baseVoiceCodes));
        
                 // 5. 根据压力传感器状态添加额外表情代码
         String sensorKey = Arrays.toString(pressureSensors);
         Map<Integer, String[]> sensorEnhancementExpression = PRESSURE_ENHANCEMENT_EXPRESSION_CODES.get(sensorKey);
         
         if (sensorEnhancementExpression != null) {
             String[] additionalExpressionCodes = sensorEnhancementExpression.get(0); // 所有反馈类别都使用同一套增强代码
             if (additionalExpressionCodes != null) {
                 enhancedExpressionPool.addAll(Arrays.asList(additionalExpressionCodes));
                 Log.d(TAG, "压力传感器 " + sensorKey + " 为反馈类别 " + feedbackCategory + " 添加了 " + additionalExpressionCodes.length + " 个额外表情代码");
             }
         }
         
         // 6. 根据压力传感器状态添加额外语音代码
         Map<Integer, String[]> sensorEnhancementVoice = PRESSURE_ENHANCEMENT_VOICE_CODES.get(sensorKey);
         
         if (sensorEnhancementVoice != null) {
             String[] additionalVoiceCodes = sensorEnhancementVoice.get(0); // 所有反馈类别都使用同一套增强代码
             if (additionalVoiceCodes != null) {
                 enhancedVoicePool.addAll(Arrays.asList(additionalVoiceCodes));
                 Log.d(TAG, "压力传感器 " + sensorKey + " 为反馈类别 " + feedbackCategory + " 添加了 " + additionalVoiceCodes.length + " 个额外语音代码");
             }
         }
        
        Log.d(TAG, "增强后的表情代码池大小: " + enhancedExpressionPool.size());
        Log.d(TAG, "增强后的表情代码池: " + enhancedExpressionPool.toString());
        Log.d(TAG, "增强后的语音代码池大小: " + enhancedVoicePool.size());
        Log.d(TAG, "增强后的语音代码池: " + enhancedVoicePool.toString());
        
        // 7. 从增强后的池中随机选择两个代码
        if (enhancedExpressionPool.isEmpty() || enhancedVoicePool.isEmpty()) {
            Log.e(TAG, "增强后的代码池为空");
            return null;
        }
        
        String expressionCode1 = enhancedExpressionPool.get(ThreadLocalRandom.current().nextInt(enhancedExpressionPool.size()));
        String expressionCode2 = enhancedExpressionPool.get(ThreadLocalRandom.current().nextInt(enhancedExpressionPool.size()));
        
        String voiceCode1 = enhancedVoicePool.get(ThreadLocalRandom.current().nextInt(enhancedVoicePool.size()));
        String voiceCode2 = enhancedVoicePool.get(ThreadLocalRandom.current().nextInt(enhancedVoicePool.size()));
        
        // 8. 分离表情反馈值(A/B/C开头)和语音反馈值(S开头)
        String expressionFeedback = null;
        String voiceFeedback = null;
        
        if (expressionCode1.startsWith("A") || expressionCode1.startsWith("B") || expressionCode1.startsWith("C")) {
            expressionFeedback = expressionCode1;
        }
        if (expressionCode2.startsWith("A") || expressionCode2.startsWith("B") || expressionCode2.startsWith("C")) {
            if (expressionFeedback == null) {
                expressionFeedback = expressionCode2;
            }
        }
        
        if (voiceCode1.startsWith("S")) {
            voiceFeedback = voiceCode1;
        }
        if (voiceCode2.startsWith("S")) {
            if (voiceFeedback == null) {
                voiceFeedback = voiceCode2;
            }
        }
        
        // 9. 如果某个类型的反馈值为空，再次随机选择
        if (expressionFeedback == null || voiceFeedback == null) {
            for (int i = 0; i < 10 && (expressionFeedback == null || voiceFeedback == null); i++) {
                String randomExpressionCode = enhancedExpressionPool.get(ThreadLocalRandom.current().nextInt(enhancedExpressionPool.size()));
                String randomVoiceCode = enhancedVoicePool.get(ThreadLocalRandom.current().nextInt(enhancedVoicePool.size()));
                
                if (randomExpressionCode.startsWith("A") || randomExpressionCode.startsWith("B") || randomExpressionCode.startsWith("C")) {
                    if (expressionFeedback == null) {
                        expressionFeedback = randomExpressionCode;
                    }
                }
                if (randomVoiceCode.startsWith("S")) {
                    if (voiceFeedback == null) {
                        voiceFeedback = randomVoiceCode;
                    }
                }
            }
        }
        
        Log.i(TAG, String.format("生成反馈代码: 表情反馈值=%s, 语音反馈值=%s", expressionFeedback, voiceFeedback));
        return new FeedbackCodes(expressionFeedback, voiceFeedback);
    }

    /**
     * 反馈代码类
     */
    public static class FeedbackCodes {
        public final String expressionFeedback;  // 表情反馈值 (A/B/C开头)
        public final String voiceFeedback;       // 语音反馈值 (S开头)
        
        public FeedbackCodes(String expressionFeedback, String voiceFeedback) {
            this.expressionFeedback = expressionFeedback;
            this.voiceFeedback = voiceFeedback;
        }
        
        @Override
        public String toString() {
            return "FeedbackCodes{" +
                    "expressionFeedback='" + expressionFeedback + '\'' +
                    ", voiceFeedback='" + voiceFeedback + '\'' +
                    '}';
        }
    }
} 