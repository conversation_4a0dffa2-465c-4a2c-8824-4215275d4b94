package com.my.emotionsdkdemo.ui;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.util.Log;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

// 添加音频设备诊断相关的import
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.content.Context;
import android.os.Build;

import com.emotion.face.sdk.FaceBox;
import com.my.emotionsdkdemo.databinding.ActivityMainBinding;
import com.emotion.voice.sdk.VoiceEmotionModule;
import com.emotion.face.sdk.FaceEmotionModule;
import com.emotion.face.sdk.FaceDetectionView;
import com.my.emotionsdkdemo.fusion.EmotionFusionManager;
import com.my.emotionsdkdemo.fusion.EmotionMappingProcessor;

import java.util.ArrayList;


public class MainActivity extends AppCompatActivity {
    private ActivityMainBinding binding;
    private static final String TAG = "EmotionDemo";
    
    // 权限请求码
    private static final int REQUEST_CAMERA_PERMISSION = 1001;
    private static final int REQUEST_AUDIO_PERMISSION = 1002;
    
    // SDK模块接口
    private VoiceEmotionModule voiceModule;
    private FaceEmotionModule faceModule;
    private FaceDetectionView faceDetectionView;
    private EmotionFusionManager fusionManager;

    // 外部摄像头管理器
    private com.emotion.face.sdk.ExternalCameraManager externalCameraManager;
    
    // 状态管理
    private final AtomicBoolean isVoiceRunning = new AtomicBoolean(false);
    private final AtomicBoolean isFaceRunning = new AtomicBoolean(false);
    private final AtomicBoolean isVoiceInitialized = new AtomicBoolean(false);
    private final AtomicBoolean pendingVoiceStart = new AtomicBoolean(false); // 用户是否尝试启动过语音检测
    private final AtomicInteger voiceDetectionCount = new AtomicInteger(0);
    private final AtomicInteger faceBatchCount = new AtomicInteger(0);
    private final AtomicInteger voiceBatchCount = new AtomicInteger(0);

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 创建语音检测模块
        int voiceBatchSize = 5; // 每5个情感结果触发一次批次回调
        voiceModule = new VoiceEmotionModule(this, voiceBatchSize);
        voiceModule.setCallback(voiceModuleCallback);
        
        // 创建人脸检测模块
        int faceBatchFrames = 30; // 30帧批次处理
        faceModule = new FaceEmotionModule(this, faceBatchFrames);
        faceModule.setCallback(faceModuleCallback);

        // 设置按钮点击事件
        binding.btnStartRealtimeDetection.setOnClickListener(v -> toggleFaceDetection());
        binding.btnTestVoiceDetection.setOnClickListener(v -> toggleVoiceDetection());
        
        // 初始化组件
        initializeComponents();
    }
    
    /**
     * 初始化核心组件
     */
    private void initializeComponents() {
        Log.i(TAG, "初始化重构版SDK模块");
        Log.d(TAG, "[INIT] 初始化前状态 - voice_running:" + isVoiceRunning.get() + " voice_pending:" + pendingVoiceStart.get());
        
        // 设置人脸覆盖层
        faceDetectionView = binding.faceDetectionView;
        faceModule.setDisplayView(faceDetectionView);
        
        // 初始化情感融合管理器
        fusionManager = new EmotionFusionManager(this);
        fusionManager.setCallback(fusionCallback);
        Log.i(TAG, "情感融合管理器初始化完成");
        
        // 检查权限
        checkPermissions();
        
        // 主动初始化语音模块（如果有权限的话）
        if (checkAudioPermission()) {
            Log.i(TAG, "检测到录音权限，主动初始化语音模块");
            Log.d(TAG, "[INIT] 初始化语音模块前 - pending:" + pendingVoiceStart.get());
            voiceModule.initialize();
            Log.d(TAG, "[INIT] 初始化语音模块后 - pending:" + pendingVoiceStart.get());
        } else {
            Log.w(TAG, "录音权限未授予，语音模块将延迟初始化");
        }
        
        Log.i(TAG, "SDK模块初始化完成");
        Log.d(TAG, "[INIT] 初始化后状态 - voice_running:" + isVoiceRunning.get() + " voice_pending:" + pendingVoiceStart.get());
        Log.i(TAG, "语音检测模式: 0.5秒音频块处理 + 智能VAD检测");
        Log.i(TAG, "人脸检测模式: 优化接口 + 双缓冲显示 + 批次处理");
    }
    
    /**
     * 检查所需权限
     */
    private void checkPermissions() {
        if (!checkCameraPermission()) {
            requestCameraPermission();
        }
        if (!checkAudioPermission()) {
            requestAudioPermission();
        } else {
            // 权限已有，进行深度音频设备诊断
            performAudioDeviceDiagnostics();
        }
    }
    
    // 权限管理
    private boolean checkCameraPermission() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) 
                == PackageManager.PERMISSION_GRANTED;
    }
    
    private boolean checkAudioPermission() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) 
                == PackageManager.PERMISSION_GRANTED;
    }
    
    private void requestCameraPermission() {
        ActivityCompat.requestPermissions(this, 
                new String[]{Manifest.permission.CAMERA}, 
                REQUEST_CAMERA_PERMISSION);
    }
    
    private void requestAudioPermission() {
        ActivityCompat.requestPermissions(this, 
                new String[]{Manifest.permission.RECORD_AUDIO}, 
                REQUEST_AUDIO_PERMISSION);
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == REQUEST_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // Log.i(TAG, "摄像头权限已授予");
                // 权限授予后不自动启动人脸检测，等用户手动启动
            } else {
                Log.w(TAG, "摄像头权限被拒绝，人脸检测功能不可用");
            }
        } else if (requestCode == REQUEST_AUDIO_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.i(TAG, "录音权限已授予，开始音频设备诊断");
                // 权限授予后立即进行设备诊断
                performAudioDeviceDiagnostics();
                
                // 只有在用户真正点击了按钮才启动，避免自动启动
                if (pendingVoiceStart.get()) {
                    Log.i(TAG, "权限获取后，响应用户的启动请求");
                    startVoiceDetection(false);
                } else {
                    Log.d(TAG, "权限获取后，但用户未请求启动，跳过自动启动");
                }
            } else {
                Log.w(TAG, "录音权限被拒绝，语音检测功能不可用");
                // 权限被拒绝时也要清除待启动标志
                pendingVoiceStart.set(false);
            }
        }
    }
    
    // 语音检测控制
    private void toggleVoiceDetection() {
        if (isVoiceRunning.get()) {
            stopVoiceDetection();
        } else {
            startVoiceDetection();
        }
    }
    
    private void startVoiceDetection() {
        startVoiceDetection(true);
    }
    
    private void startVoiceDetection(boolean requestPermissionIfNeeded) {
        // 标记用户尝试启动语音检测
        pendingVoiceStart.set(true);
        
        if (!checkAudioPermission()) {
            if (requestPermissionIfNeeded) {
                Log.w(TAG, "缺少录音权限，请求权限");
                requestAudioPermission();
            } else {
                Log.e(TAG, "缺少录音权限，无法启动语音检测");
                pendingVoiceStart.set(false); // 清除标志
            }
            return;
        }
        
        Log.i(TAG, "=== 启动语音检测 ===");
        
        // 检查语音模块是否已初始化
        if (!isVoiceInitialized.get()) {
            Log.w(TAG, "语音模块未初始化，尝试初始化");
            voiceModule.initialize();
            return; // 等待初始化完成后的回调
        }
        
        // 启动语音检测
        if (voiceModule.startDetection()) {
            isVoiceRunning.set(true);
            voiceDetectionCount.set(0);
            voiceBatchCount.set(0);
            binding.btnTestVoiceDetection.setText("停止语音检测");
            
            // 启动融合管理器
            if (fusionManager != null && !fusionManager.isRunning()) {
                fusionManager.startFusion();
                Log.i(TAG, "情感融合管理器已启动");
            }
            
            Log.i(TAG, "语音检测启动成功");
        } else {
            Log.e(TAG, "语音检测启动失败");
            pendingVoiceStart.set(false); // 清除标志
        }
    }
    
    private void stopVoiceDetection() {
        Log.i(TAG, "=== 停止语音检测 ===");
        Log.d(TAG, "[VOICE] 停止前状态 - running:" + isVoiceRunning.get() + " pending:" + pendingVoiceStart.get());
        
        if (voiceModule != null) {
            voiceModule.stopDetection();
        }
        
        isVoiceRunning.set(false);
        pendingVoiceStart.set(false); // 清除待启动标志
        binding.btnTestVoiceDetection.setText("开始语音检测");
        
        // 检查是否需要停止融合管理器
        if (fusionManager != null && fusionManager.isRunning() && !isFaceRunning.get()) {
            fusionManager.stopFusion();
            Log.i(TAG, "情感融合管理器已停止");
        }
        
        int totalDetections = voiceDetectionCount.get();
        int totalBatches = voiceBatchCount.get();
        Log.i(TAG, String.format("语音检测已停止 - 总检测次数: %d, 批次数: %d", 
            totalDetections, totalBatches));
        Log.d(TAG, "[VOICE] 停止后状态 - running:" + isVoiceRunning.get() + " pending:" + pendingVoiceStart.get());
    }
    
    // 人脸检测控制
    private void toggleFaceDetection() {
        if (isFaceRunning.get()) {
            stopFaceDetection();
        } else {
            startFaceDetection();
        }
    }
    
    private void startFaceDetection() {
        // ========== 原有摄像头模式 ==========
        /*
        if (!checkCameraPermission()) {
            Log.w(TAG, "缺少摄像头权限，无法启动人脸检测");
            requestCameraPermission();
            return;
        }

        Log.i(TAG, "=== 启动人脸检测 ===");
        Log.d(TAG, "[FACE] 启动前状态 - voice_running:" + isVoiceRunning.get() + " voice_pending:" + pendingVoiceStart.get());

        binding.textureView.setVisibility(android.view.View.VISIBLE);
        binding.faceDetectionView.setVisibility(android.view.View.VISIBLE);

        if (faceModule != null) {
            // 初始化模块
            faceModule.initialize(binding.textureView);

            // 启动检测
            if (faceModule.startDetection()) {
                isFaceRunning.set(true);
                faceBatchCount.set(0);
                binding.btnStartRealtimeDetection.setText("停止人脸检测");

                // 启动融合管理器
                if (fusionManager != null && !fusionManager.isRunning()) {
                    fusionManager.startFusion();
                    Log.i(TAG, "情感融合管理器已启动");
                }

                Log.i(TAG, "人脸检测启动成功");
            } else {
                Log.e(TAG, "人脸检测启动失败");
            }
        }
        */

        // ========== 新的外部摄像头管理模式 ==========
        Log.i(TAG, "=== 启动人脸检测（外部摄像头管理模式）===");
        Log.d(TAG, "[FACE] 启动前状态 - voice_running:" + isVoiceRunning.get() + " voice_pending:" + pendingVoiceStart.get());

        // 检查摄像头权限（外部摄像头管理仍需要权限）
        if (!checkCameraPermission()) {
            Log.w(TAG, "缺少摄像头权限，无法启动外部摄像头管理");
            requestCameraPermission();
            return;
        }

        // 显示摄像头预览和检测结果视图
        binding.textureView.setVisibility(android.view.View.VISIBLE);
        binding.faceDetectionView.setVisibility(android.view.View.VISIBLE);

        // 调试：输出TextureView的尺寸信息
//        binding.textureView.post(() -> {
//            int width = binding.textureView.getWidth();
//            int height = binding.textureView.getHeight();
//            Log.i(TAG, "TextureView尺寸: " + width + "x" + height);
//        });

        if (faceModule != null) {
            // 初始化为外部摄像头管理模式：传入TextureView用于显示，但使用外部摄像头管理
            faceModule.initializeWithExternalCamera(binding.textureView, true);

            // 注意：不在这里调用startDetection()，而是在初始化成功回调中处理
            // 这样可以避免时序问题和重复调用
            Log.i(TAG, "人脸模块初始化请求已发送，等待回调处理启动");
        }
    }

    /**
     * 启动外部摄像头管理
     * 使用独立的摄像头管理，获取帧数据后传递给检测模块
     */
    private void startExternalCameraManagement() {
        Log.i(TAG, "启动外部摄像头管理");

        // 创建外部摄像头管理器
        externalCameraManager = new com.emotion.face.sdk.ExternalCameraManager(this);

        // 设置摄像头回调
        externalCameraManager.setCallback(new com.emotion.face.sdk.ExternalCameraManager.ExternalCameraCallback() {
            @Override
            public void onCameraOpened() {
                Log.i(TAG, "外部摄像头已打开");
            }

            @Override
            public void onCameraReady() {
                Log.i(TAG, "外部摄像头就绪，开始接收帧数据");
            }

            @Override
            public void onCameraDisconnected() {
                Log.w(TAG, "外部摄像头断开连接");
            }

            @Override
            public void onFrameAvailable(android.graphics.Bitmap bitmap) {
                // 外部摄像头帧数据传递给检测模块
                if (faceModule != null && isFaceRunning.get() && bitmap != null) {
                    faceModule.processFrame(bitmap);
                }
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "外部摄像头错误: " + error);
            }
        });

        // 初始化并启动外部摄像头
        externalCameraManager.initialize();
        externalCameraManager.startCamera();

        Log.i(TAG, "外部摄像头管理启动完成");
    }


    
    private void stopFaceDetection() {
        Log.i(TAG, "=== 停止人脸检测（外部摄像头管理模式）===");
        Log.d(TAG, "[FACE] 停止前状态 - running:" + isFaceRunning.get());

        // 停止人脸检测模块
        if (faceModule != null) {
            faceModule.stopDetection();
        }

        // 停止外部摄像头管理器
        if (externalCameraManager != null) {
            externalCameraManager.stopCamera();
            externalCameraManager.release();
            externalCameraManager = null;
            Log.i(TAG, "外部摄像头管理器已停止并释放");
        }

        isFaceRunning.set(false);
        binding.btnStartRealtimeDetection.setText("开始人脸检测");

        // 检查是否需要停止融合管理器
        if (fusionManager != null && fusionManager.isRunning() && !isVoiceRunning.get()) {
            fusionManager.stopFusion();
            Log.i(TAG, "情感融合管理器已停止");
        }

        // 隐藏摄像头预览和检测结果视图
        binding.textureView.setVisibility(android.view.View.GONE);
        binding.faceDetectionView.setVisibility(android.view.View.GONE);

        int totalBatches = faceBatchCount.get();
        Log.i(TAG, String.format("人脸检测已停止（外部摄像头管理模式）- 总批次数: %d", totalBatches));
        Log.d(TAG, "[FACE] 停止后状态 - running:" + isFaceRunning.get());
    }
    
    // 语音模块回调 
    private final VoiceEmotionModule.VoiceModuleCallback voiceModuleCallback = 
        new VoiceEmotionModule.VoiceModuleCallback() {
        
        @Override
        public void onInitializationSuccess() {
                    Log.d(TAG, "[VOICE] 语音模块初始化成功");
        Log.d(TAG, "[VOICE] pendingVoiceStart状态: " + pendingVoiceStart.get());
        Log.d(TAG, "[VOICE] 当前线程: " + Thread.currentThread().getName());
            
            isVoiceInitialized.set(true);
            
            // 如果已经尝试启动检测，现在可以真正启动
            if (pendingVoiceStart.get()) {
                Log.w(TAG, "[VOICE] 检测到待启动请求，自动启动语音检测");
                Log.d(TAG, "[VOICE] 调用栈: " + android.util.Log.getStackTraceString(new Throwable()));
                
                // 直接启动检测，不再调用startVoiceDetection避免递归
                if (voiceModule.startDetection()) {
                    isVoiceRunning.set(true);
                    voiceDetectionCount.set(0);
                    voiceBatchCount.set(0);
                    runOnUiThread(() -> binding.btnTestVoiceDetection.setText("停止语音检测"));
                    pendingVoiceStart.set(false);
                    Log.i(TAG, "[VOICE] 语音检测自动启动成功");
                } else {
                    pendingVoiceStart.set(false);
                    Log.e(TAG, "[VOICE] 语音检测自动启动失败");
                }
            } else {
                Log.d(TAG, "[VOICE] 没有待启动请求，跳过自动启动");
            }
        }
        
        @Override
        public void onInitializationError(String error) {
            Log.e(TAG, "语音模块初始化失败: " + error);
            isVoiceInitialized.set(false);
            pendingVoiceStart.set(false); // 清除待启动标志
        }
        
        @Override
        public void onDetectionStarted() {
            // Log.i(TAG, "语音检测已开始");
        }
        
        @Override
        public void onDetectionStopped() {
            // Log.i(TAG, "语音检测已停止");
        }
        
        @Override
        public void onFrameProcessed(String emotion, int frameIndex) {
            voiceDetectionCount.incrementAndGet();
            Log.d(TAG, String.format("语音帧 #%d: %s", frameIndex, emotion));
            

        }
        
        @Override
        public void onBatchComplete(List<String> emotions, int batchIndex) {
            int currentBatchNumber = voiceBatchCount.incrementAndGet();
            Log.i(TAG, String.format("语音情感批次 #%d: 检测到 %d个情感 %s", 
                currentBatchNumber, emotions.size(), emotions.toString()));
            
            // 将整个批次的情感结果传入融合管理器
            for (String emotion : emotions) {
                if (emotion != null && !emotion.trim().isEmpty()) {
                    fusionManager.addVoiceEmotion(emotion);
                }
            }
            Log.i(TAG, String.format("语音批次情感传入融合器: %s", emotions.toString()));
        }
        
        @Override
        public void onError(String error) {
            Log.e(TAG, "语音检测错误: " + error);
        }
        
        @Override
        public void onVadStateChanged(boolean hasVoice, String stateDescription) {
            Log.d(TAG, "VAD状态变化: " + (hasVoice ? "检测到人声" : "静音") + " - " + stateDescription);
            
            // 将VAD状态传递给融合管理器
            if (fusionManager != null) {
                fusionManager.updateVoiceActivity(hasVoice);
            }
        }
    };
    
    /**
     * 人脸模块回调
     */
    private final FaceEmotionModule.FaceModuleCallback faceModuleCallback = 
        new FaceEmotionModule.FaceModuleCallback() {

        @Override
        public void onInitializationSuccess() {
            Log.i(TAG, "人脸模块初始化成功");
            if (faceModule != null) {
                boolean success = faceModule.startDetection();
                if (success) {
                    isFaceRunning.set(true);
                    faceBatchCount.set(0);
                    binding.btnStartRealtimeDetection.setText("停止人脸检测");

                    // 启动融合管理器
                    if (fusionManager != null && !fusionManager.isRunning()) {
                        fusionManager.startFusion();
                        Log.i(TAG, "情感融合管理器已启动");
                    }

                    Log.i(TAG, "人脸检测启动成功（外部摄像头管理模式）");

                    // 启动外部摄像头管理
                    startExternalCameraManagement();

                } else {
                    Log.e(TAG, "人脸检测在回调中启动失败");
                    binding.textureView.setVisibility(android.view.View.GONE);
                    binding.faceDetectionView.setVisibility(android.view.View.GONE);
                }
            }
        }
        
        @Override
        public void onInitializationError(String error) {
            Log.e(TAG, "人脸模块初始化失败: " + error);
        }
        
        @Override
        public void onDetectionStarted() {
            // Log.i(TAG, "人脸检测已启动");
        }
        
        @Override
        public void onDetectionStopped() {
            // Log.i(TAG, "人脸检测已停止");
        }
        
        @Override
        public void onFrameProcessed(List<FaceBox> results, int frameIndex) {
            if (results != null && !results.isEmpty()) {
                Log.i(TAG, String.format("帧 #%d: 检测到 %d 个人脸", frameIndex, results.size()));
            }
        }
        
        @Override
        public void onBatchComplete(List<FaceBox[]> batchResults, int batchIndex) {
            int currentBatchNumber = faceBatchCount.incrementAndGet();
            
            // 统计批次中的人脸数量和情感
            int totalFaces = 0;
            List<String> batchEmotions = new ArrayList<>();
            
            for (FaceBox[] frameResults : batchResults) {
                totalFaces += frameResults.length;
                // 收集每一帧的情感结果
                for (FaceBox faceBox : frameResults) {
                    if (faceBox.emotion != null && !faceBox.emotion.trim().isEmpty()) {
                        batchEmotions.add(faceBox.emotion);
                    }
                }
            }
            
            if (totalFaces > 0) {
                Log.i(TAG, String.format("人脸批次 #%d: 检测到 %d 个人脸，%d 个情感结果", 
                    currentBatchNumber, totalFaces, batchEmotions.size()));
                // 将整个批次的情感结果传入融合管理器
                for (String emotion : batchEmotions) {
                    fusionManager.addFaceEmotion(emotion);
                }
                Log.i(TAG, String.format("人脸批次情感传入融合器: %s", batchEmotions.toString()));
            }
        }
        
        @Override
        public void onPerformanceUpdate(double fps, double avgProcessTime) {
            // Log.d(TAG, String.format("人脸检测性能: FPS=%.1f, 平均处理时间=%.1fms", 
            //     fps, avgProcessTime));
        }
        
        @Override
        public void onError(String error) {
            Log.e(TAG, "人脸检测错误: " + error);
        }
    };
    
    /**
     * 情感融合管理器回调
     */
    private final EmotionFusionManager.FusionCallback fusionCallback = 
        new EmotionFusionManager.FusionCallback() {

        // 回调成功 处理完成
        @Override
        public void onFusionResult(String fusedEmotion, boolean hasVoice, boolean hasFace, 
                                 int faceCount, int voiceCount, double confidence, 
                                 EmotionMappingProcessor.CompleteEmotionResult completeResult) {

            Log.i(TAG, "========== 情感融合结果 ==========");
            Log.i(TAG, "融合情感: " + fusedEmotion);
            Log.i(TAG, "置信度: " + String.format("%.2f", confidence));
            Log.i(TAG, "数据来源: 人脸(" + faceCount + "个) + 语音(" + voiceCount + "个)");
            Log.i(TAG, "活动状态: 人声=" + hasVoice + ", 人脸=" + hasFace);

            if (completeResult != null) {
                Log.i(TAG, "---------- 映射处理结果 ----------");
                Log.i(TAG, "主导情感: " + completeResult.basicResult.dominantEmotion);
                if (completeResult.basicResult.processedArray != null) {
                    Log.i(TAG, "处理数组: " + java.util.Arrays.toString(completeResult.basicResult.processedArray));
                }
                Log.i(TAG, "反馈类别: " + completeResult.feedbackCategory);
                if (completeResult.pressureSensors != null) {
                    Log.i(TAG, "压力传感器: " + java.util.Arrays.toString(completeResult.pressureSensors));
                }
                if (completeResult.feedbackCodes != null) {
                    Log.i(TAG, "表情反馈: " + completeResult.feedbackCodes.expressionFeedback);
                    Log.i(TAG, "语音反馈: " + completeResult.feedbackCodes.voiceFeedback);
                }
            }
            Log.i(TAG, "=====================================");

            // 更新UI显示
            runOnUiThread(() -> {
                binding.tvSystemStatus.setText(String.format(
                    "融合结果: %s (置信度: %.2f) | 反馈类别: %d",
                    fusedEmotion, confidence,
                    completeResult != null ? completeResult.feedbackCategory : -1));
            });
        }

        // 融合跳过
        @Override
        public void onFusionSkipped(String reason, boolean hasVoice, boolean hasFace) {
            Log.d(TAG, "融合跳过: " + reason + " (人声=" + hasVoice + ", 人脸=" + hasFace + ")");
        }

        // 性能统计回调
        @Override
        public void onPerformanceUpdate(long totalFusions, long successfulFusions, double successRate) {
            Log.d(TAG, String.format("融合性能: 总计%d次, 成功%d次, 成功率%.1f%%", 
                totalFusions, successfulFusions, successRate));
        }
    };
    
    /**
     * 执行音频设备诊断
     */
    private void performAudioDeviceDiagnostics() {
        // Log.i(TAG, "=== 开始音频设备诊断 ===");
        
        try {
            // 1. 检查系统音频服务
            AudioManager audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
            if (audioManager == null) {
                Log.e(TAG, "系统音频服务不可用");
                return;
            }
            
            // Log.i(TAG, "1. 音频系统基本信息:");
            // Log.i(TAG, "  - 音频模式: " + audioManager.getMode());
            // Log.i(TAG, "  - 音量: " + audioManager.getStreamVolume(AudioManager.STREAM_MUSIC));
            // Log.i(TAG, "  - 最大音量: " + audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC));
            
            // 2. 检查麦克风可用性
            boolean microphoneAvailable = getPackageManager().hasSystemFeature(PackageManager.FEATURE_MICROPHONE);
            // Log.i(TAG, "2. 麦克风硬件: " + (microphoneAvailable ? "可用" : "不可用"));
            
            // 3. 检查音频录制参数兼容性
            // Log.i(TAG, "3. 音频录制参数兼容性检查:");
            int sampleRate = 16000;
            int channelConfig = AudioFormat.CHANNEL_IN_MONO;
            int audioFormat = AudioFormat.ENCODING_PCM_16BIT;
            
            int minBufferSize = AudioRecord.getMinBufferSize(sampleRate, channelConfig, audioFormat);
            // Log.i(TAG, "  - 最小缓冲区大小: " + minBufferSize);
            
            if (minBufferSize == AudioRecord.ERROR_BAD_VALUE) {
                Log.e(TAG, "音频参数组合不支持");
                tryAlternativeAudioConfigs();
                return;
            }
            
            if (minBufferSize == AudioRecord.ERROR) {
                Log.e(TAG, "音频参数查询失败");
                return;
            }
            
            // 4. 尝试创建AudioRecord测试
            // Log.i(TAG, "4. 创建AudioRecord测试...");
            AudioRecord testRecord = null;
            try {
                int bufferSize = Math.max(minBufferSize, 4096);
                testRecord = new AudioRecord(
                    MediaRecorder.AudioSource.MIC,
                    sampleRate,
                    channelConfig,
                    audioFormat,
                    bufferSize
                );
                
                int state = testRecord.getState();
                // Log.i(TAG, "  - AudioRecord状态: " + state);
                // Log.i(TAG, "  - 预期状态: " + AudioRecord.STATE_INITIALIZED);
                
                if (state == AudioRecord.STATE_INITIALIZED) {
                    // Log.i(TAG, "✅ AudioRecord创建成功");
                    
                    // 5. 测试录音启动
                    // Log.i(TAG, "5. 测试录音启动...");
                    testRecord.startRecording();
                    
                    int recordingState = testRecord.getRecordingState();
                    // Log.i(TAG, "  - 录音状态: " + recordingState);
                    // Log.i(TAG, "  - 预期状态: " + AudioRecord.RECORDSTATE_RECORDING);
                    
                    if (recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                        // Log.i(TAG, "✅ 录音启动成功");
                        
                        // 6. 简单数据读取测试
                        short[] testBuffer = new short[160];
                        int samplesRead = testRecord.read(testBuffer, 0, 160);
                        // Log.i(TAG, "6. 数据读取测试: " + samplesRead + " 样本");
                        
                        if (samplesRead > 0) {
                            // Log.i(TAG, "✅ 音频数据读取成功");
                            
                            // 计算音频数据的基本统计
                            double sum = 0;
                            for (int i = 0; i < samplesRead; i++) {
                                sum += Math.abs(testBuffer[i]);
                            }
                            double avgAmplitude = sum / samplesRead;
                            // Log.i(TAG, "  - 平均振幅: " + avgAmplitude);
                            
                            if (avgAmplitude > 1.0) {
                                // Log.i(TAG, "✅ 检测到音频输入活动");
                            } else {
                                // Log.w(TAG, "⚠️ 音频输入振幅很低，可能是静音环境或设备问题");
                            }
                            
                        } else {
                            // Log.w(TAG, "⚠️ 无法读取音频数据: " + samplesRead);
                        }
                        
                        testRecord.stop();
                        // Log.i(TAG, "录音测试停止");
                        
                    } else {
                        Log.e(TAG, "录音启动失败");
                        Log.e(TAG, "可能原因:");
                        Log.e(TAG, "  - 音频设备被其他应用占用");
                        Log.e(TAG, "  - 系统音频服务异常");
                        Log.e(TAG, "  - 硬件故障");
                    }
                    
                } else {
                    Log.e(TAG, "AudioRecord创建失败");
                    Log.e(TAG, "状态码: " + state);
                    Log.e(TAG, "可能原因:");
                    Log.e(TAG, "  - 权限问题（尽管已授予）");
                    Log.e(TAG, "  - 音频硬件不支持指定参数");
                    Log.e(TAG, "  - 系统资源不足");
                }
                
            } catch (SecurityException e) {
                Log.e(TAG, "安全异常: " + e.getMessage());
                Log.e(TAG, "权限可能未真正生效，请检查系统设置");
            } catch (IllegalArgumentException e) {
                Log.e(TAG, "参数异常: " + e.getMessage());
                Log.e(TAG, "音频参数配置错误");
            } catch (Exception e) {
                Log.e(TAG, "音频设备测试异常: " + e.getMessage());
                Log.e(TAG, "异常详情", e);
            } finally {
                if (testRecord != null) {
                    try {
                        testRecord.release();
                    } catch (Exception e) {
                        Log.w(TAG, "释放测试AudioRecord时出错: " + e.getMessage());
                    }
                }
            }
            
            // 7. 系统信息诊断
            // Log.i(TAG, "7. 系统信息:");
            // Log.i(TAG, "  - Android版本: " + Build.VERSION.RELEASE);
            // Log.i(TAG, "  - API级别: " + Build.VERSION.SDK_INT);
            // Log.i(TAG, "  - 设备型号: " + Build.MODEL);
            // Log.i(TAG, "  - 制造商: " + Build.MANUFACTURER);
            
            // 8. 权限状态最终确认
            // Log.i(TAG, "8. 权限状态最终确认:");
            int permissionStatus = ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO);
            // Log.i(TAG, "  - RECORD_AUDIO权限: " + (permissionStatus == PackageManager.PERMISSION_GRANTED ? "已授予" : "未授予"));
            
            if (permissionStatus != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "检测到权限状态不一致，建议重新申请权限");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "音频设备诊断失败: " + e.getMessage());
            Log.e(TAG, "详细异常信息", e);
        }
        
        // Log.i(TAG, "=== 音频设备诊断完成 ===");
    }

    /**
     * 尝试替代音频配置
     */
    private void tryAlternativeAudioConfigs() {
        // Log.i(TAG, "尝试替代音频配置...");
        
        // 不同的采样率
        int[] sampleRates = {16000, 8000, 44100, 22050};
        // 不同的声道配置
        int[] channelConfigs = {AudioFormat.CHANNEL_IN_MONO, AudioFormat.CHANNEL_IN_STEREO};
        // 不同的音频格式
        int[] audioFormats = {AudioFormat.ENCODING_PCM_16BIT, AudioFormat.ENCODING_PCM_8BIT};
        
        for (int sampleRate : sampleRates) {
            for (int channelConfig : channelConfigs) {
                for (int audioFormat : audioFormats) {
                    int minBufferSize = AudioRecord.getMinBufferSize(sampleRate, channelConfig, audioFormat);
                    if (minBufferSize != AudioRecord.ERROR_BAD_VALUE && minBufferSize != AudioRecord.ERROR) {
                        // Log.i(TAG, String.format("✅ 支持的配置: %dHz, 声道=%d, 格式=%d, 缓冲区=%d", 
                        //     sampleRate, channelConfig, audioFormat, minBufferSize));
                    }
                }
            }
        }
    }
    
    // 生命周期管理
    @Override
    protected void onPause() {
        super.onPause();
        // Log.d(TAG, "Activity暂停");
        
        if (faceModule != null && isFaceRunning.get()) {
            faceModule.pauseDetection();
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // Log.d(TAG, "Activity恢复");
        
        if (faceModule != null && isFaceRunning.get()) {
            faceModule.resumeDetection();
        }
    }
    














    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        // Log.i(TAG, "=== 销毁主界面，释放资源 ===");
        
        // 释放语音模块
        if (voiceModule != null) {
            voiceModule.release();
            voiceModule = null;
        }
        
        // 释放人脸模块
        if (faceModule != null) {
            faceModule.release();
            faceModule = null;
        }
        
        // 清理对象池
        try {
            com.emotion.face.sdk.FaceMemoryMonitor.cleanupFacePools();
            com.emotion.voice.sdk.VoiceObjectPool.cleanup();
        } catch (Exception e) {
            Log.w(TAG, "对象池清理异常: " + e.getMessage());
        }
        
        // Log.i(TAG, "主界面资源释放完成");
    }
}


