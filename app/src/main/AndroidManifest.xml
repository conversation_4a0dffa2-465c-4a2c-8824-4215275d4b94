<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 摄像头权限 -->
    <uses-permission android:name="android.permission.CAMERA" />
    
    <!-- 录音权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    
    <!-- 摄像头硬件要求 -->
    <uses-feature 
        android:name="android.hardware.camera" 
        android:required="true" />
    <uses-feature 
        android:name="android.hardware.camera.autofocus" 
        android:required="false" />
    
    <!-- 麦克风硬件要求 -->
    <uses-feature 
        android:name="android.hardware.microphone" 
        android:required="true" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.EmotionSdkDemo"
        tools:targetApi="31">
        
        <!-- 主Activity - 包含人脸识别和语音检测功能 -->
        <activity
            android:name=".ui.MainActivity"
            android:exported="true"
            android:label="@string/app_name">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <!-- VAD测试页面 - 独立的人声活动检测测试功能 -->
        <activity
            android:name=".ui.VadTestActivity"
            android:exported="false"
            android:label="VAD测试" />

    </application>

</manifest>