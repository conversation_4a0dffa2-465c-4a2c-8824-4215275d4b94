<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:context=".ui.MainActivity">

    <!-- 实时预览容器 - 320x320dp固定尺寸 -->
    <FrameLayout
        android:id="@+id/displayContainer"
        android:layout_width="320dp"
        android:layout_height="320dp"
        android:layout_marginTop="24dp"
        android:background="#f5f5f5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 摄像头预览区域 -->
        <TextureView
            android:id="@+id/textureView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

        <!-- 人脸检测显示组件 - 作为覆盖层显示在TextureView之上 -->
        <com.emotion.face.sdk.FaceDetectionView
            android:id="@+id/faceDetectionView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible"
            android:background="@android:color/transparent" />

    </FrameLayout>

    <!-- 人脸检测按钮 -->
    <Button
        android:id="@+id/btnStartRealtimeDetection"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_marginTop="24dp"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="8dp"
        android:text="开始人脸检测"
        android:textSize="16sp"
        android:textStyle="bold"
        android:background="@android:color/holo_green_light"
        android:textColor="@android:color/white"
        app:layout_constraintEnd_toStartOf="@id/btnTestVoiceDetection"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/displayContainer" />

    <!-- 语音检测按钮 -->
    <Button
        android:id="@+id/btnTestVoiceDetection"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_marginTop="24dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="32dp"
        android:text="开始语音检测"
        android:textSize="16sp"
        android:textStyle="bold"
        android:background="@android:color/holo_purple"
        android:textColor="@android:color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnStartRealtimeDetection"
        app:layout_constraintTop_toBottomOf="@id/displayContainer" />

    <!-- VAD测试页面跳转按钮 -->
    <Button
        android:id="@+id/btn_navigate_to_test_page"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_marginTop="16dp"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:text="跳转到VAD测试页"
        android:textSize="16sp"
        android:textStyle="bold"
        android:background="@android:color/holo_orange_light"
        android:textColor="@android:color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btnTestVoiceDetection" />

    <!-- 识别结果显示区域 -->
    <LinearLayout
        android:id="@+id/resultLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:orientation="vertical"
        android:background="#f8f8f8"
        android:padding="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_navigate_to_test_page">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="实时识别结果"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:gravity="center" />

        <!-- 人脸检测结果 -->
        <TextView
            android:id="@+id/tvFaceResult"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="人脸检测: 未启动"
            android:textSize="16sp"
            android:textColor="#666666"
            android:fontFamily="monospace"
            android:background="#ffffff"
            android:padding="12dp" />

        <!-- 语音检测结果 -->
        <TextView
            android:id="@+id/tvVoiceResult"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="语音检测: 未启动"
            android:textSize="16sp"
            android:textColor="#666666"
            android:fontFamily="monospace"
            android:background="#ffffff"
            android:padding="12dp" />

        <!-- 系统状态 -->
        <TextView
            android:id="@+id/tvSystemStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="系统状态: 正在初始化..."
            android:textSize="14sp"
            android:textColor="#888888"
            android:fontFamily="monospace"
            android:background="#ffffff"
            android:padding="12dp" />

    </LinearLayout>

    <!-- 底部间距 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:layout_marginTop="24dp"
        app:layout_constraintTop_toBottomOf="@id/resultLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>