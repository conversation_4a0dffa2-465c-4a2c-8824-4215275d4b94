plugins {
    id 'com.android.library'
}

android {
    namespace 'com.emotion.voice.sdk'
    compileSdk 35

    defaultConfig {
        minSdk 25
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
        
        ndk {
            abiFilters 'arm64-v8a'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    externalNativeBuild {
        cmake {
            path file('src/main/cpp/CMakeLists.txt')
            version '3.22.1'
        }
    }
}

dependencies {
    implementation libs.appcompat
    implementation libs.material
    implementation libs.constraintlayout
    implementation 'com.github.gkonovalov.android-vad:silero:2.0.10'


    // JTransforms for high-performance FFT computations
    implementation 'com.github.wendykierp:JTransforms:3.1'
} 