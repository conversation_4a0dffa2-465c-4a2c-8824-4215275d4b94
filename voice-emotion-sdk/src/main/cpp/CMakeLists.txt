# Voice Emotion Detection Module CMakeLists.txt
cmake_minimum_required(VERSION 3.18.1)

# 设置项目名称
project("voice_emotion_sdk")

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17 -fPIC -O2")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC -O2")

# 查找必需的库
find_library(log-lib log)
find_library(android-lib android)

# 添加头文件目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# 设置jniLibs目录路径
set(JNI_LIBS_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../jniLibs/${ANDROID_ABI})

# 验证预编译库是否存在
message(STATUS "检查语音模块预编译库:")
message(STATUS "  - JNI_LIBS_DIR: ${JNI_LIBS_DIR}")
message(STATUS "  - ANDROID_ABI: ${ANDROID_ABI}")

# 添加预编译的库 - 使用语音专用的库名称
add_library(rknnrt_voice SHARED IMPORTED)
set_target_properties(rknnrt_voice PROPERTIES IMPORTED_LOCATION ${JNI_LIBS_DIR}/librknnrt.so)

add_library(rga_voice SHARED IMPORTED)
set_target_properties(rga_voice PROPERTIES IMPORTED_LOCATION ${JNI_LIBS_DIR}/librga.so)

add_library(rknn_voice_emotion_engine SHARED IMPORTED)
set_target_properties(rknn_voice_emotion_engine PROPERTIES IMPORTED_LOCATION ${JNI_LIBS_DIR}/librknn_voice_detection_demo.so)

# 验证库文件是否存在
if(EXISTS ${JNI_LIBS_DIR}/librknnrt.so)
    message(STATUS "  ✅ librknnrt.so found")
else()
    message(WARNING "  ❌ librknnrt.so NOT found")
endif()

if(EXISTS ${JNI_LIBS_DIR}/librga.so)
    message(STATUS "  ✅ librga.so found")
else()
    message(WARNING "  ❌ librga.so NOT found")
endif()

if(EXISTS ${JNI_LIBS_DIR}/librknn_voice_detection_demo.so)
    message(STATUS "  ✅ librknn_voice_detection_demo.so found")
else()
    message(WARNING "  ❌ librknn_voice_detection_demo.so NOT found")
endif()

# 设置源文件
set(VOICE_EMOTION_SOURCES
    voice_emotion_interface.cpp
    voice_emotion_interface.h
)

# 创建共享库 - 使用新的库名称
add_library(
    voice_emotion_sdk
    SHARED
    ${VOICE_EMOTION_SOURCES}
)

# 链接库
target_link_libraries(
    voice_emotion_sdk
    ${log-lib}
    ${android-lib}
    rknnrt_voice
    rga_voice
    rknn_voice_emotion_engine
)

# 设置输出目录
set_target_properties(
    voice_emotion_sdk PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
)

# 编译时输出信息
message(STATUS "Voice Emotion SDK Configuration:")
message(STATUS "  - Project: ${PROJECT_NAME}")
message(STATUS "  - CMAKE_SYSTEM_NAME: ${CMAKE_SYSTEM_NAME}")
message(STATUS "  - CMAKE_SYSTEM_PROCESSOR: ${CMAKE_SYSTEM_PROCESSOR}")
message(STATUS "  - CMAKE_BUILD_TYPE: ${CMAKE_BUILD_TYPE}")
message(STATUS "  - Output Library: libvoice_emotion_sdk.so") 