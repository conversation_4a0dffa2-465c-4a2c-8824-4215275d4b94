/* Voice Emotion Detection Module JNI Interface */
#include <jni.h>

#ifndef _Included_com_my_emotionsdkdemo_voice_VoiceEmotionInterface
#define _Included_com_my_emotionsdkdemo_voice_VoiceEmotionInterface
#ifdef __cplusplus
extern "C" {
#endif

/*
 * Class:     com_my_emotionsdkdemo_voice_VoiceEmotionInterface
 * Method:    initVoiceModel
 * Signature: (Ljava/lang/String;)Ljava/lang/String;
 * Description: 初始化语音情感识别模型
 * Parameters:
 *   - voice_emotion_model_path: 语音情感识别模型路径
 */
JNIEXPORT jstring JNICALL Java_com_my_emotionsdkdemo_voice_VoiceEmotionInterface_initVoiceModel
        (JNIEnv *, jobject, jstring);

/*
 * Class:     com_my_emotionsdkdemo_voice_VoiceEmotionInterface
 * Method:    detectVoice
 * Signature: ([F)Ljava/lang/String;
 * Description: 检测语音情感
 * Parameters:
 *   - mfcc: MFCC特征数组 (48x39=1872个特征值)
 * Returns: 情感识别结果字符串
 */
JNIEXPORT jstring JNICALL Java_com_my_emotionsdkdemo_voice_VoiceEmotionInterface_detectVoice
        (JNIEnv *, jobject, jfloatArray);

/*
 * Class:     com_my_emotionsdkdemo_voice_VoiceEmotionInterface
 * Method:    releaseVoiceModel
 * Signature: ()V
 * Description: 释放语音检测模块资源
 */
JNIEXPORT void JNICALL Java_com_my_emotionsdkdemo_voice_VoiceEmotionInterface_releaseVoiceModel
        (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif