#include <jni.h>
#include <string>
#include <android/log.h>
#include <chrono>
#include "voice_emotion_interface.h"

#define LOG_TAG "VoiceEmotionSDK"
// #define LOG_INFO(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
// #define LOG_DEBUG(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)
#define LOG_WARN(...) __android_log_print(ANDROID_LOG_WARN, LOG_TAG, __VA_ARGS__)
#define LOG_ERROR(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// 注释掉的日志宏定义 - 只保留错误和警告日志
#define LOG_INFO(...) do {} while(0)
#define LOG_DEBUG(...) do {} while(0)

// 声明外部函数 - 使用头文件中的名称
extern "C" {
    JNIEXPORT jstring JNICALL Java_com_my_emotionsdkdemo_voice_VoiceEmotionInterface_initVoiceModel(JNIEnv *, jobject, jstring);
    JNIEXPORT void JNICALL Java_com_my_emotionsdkdemo_voice_VoiceEmotionInterface_releaseVoiceModel(JNIEnv *, jobject);
    JNIEXPORT jstring JNICALL Java_com_my_emotionsdkdemo_voice_VoiceEmotionInterface_detectVoice(JNIEnv *, jobject, jfloatArray);
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_emotion_voice_sdk_VoiceEmotionInterface_initVoiceModel(
    JNIEnv *env, 
    jobject thiz,
    jstring voice_emotion_model_path) {
    
    LOG_INFO("=== 开始初始化语音情感RKNN模型 ===");
    LOG_DEBUG("JNI函数: Java_com_emotion_voice_sdk_VoiceEmotionInterface_initVoiceModel 被调用");
    
    // 检查参数是否为空
    if (voice_emotion_model_path == nullptr) {
        LOG_ERROR("❌ voice_emotion_model_path 参数为空");
        return env->NewStringUTF("Error: voice_emotion_model_path is null");
    }
    
    // 获取模型路径字符串用于日志
    const char *voice_path = env->GetStringUTFChars(voice_emotion_model_path, 0);
    
    LOG_INFO("语音模型初始化参数:");
    LOG_INFO("1. 语音情感模型: %s", voice_path);
    
    // 释放字符串资源
    env->ReleaseStringUTFChars(voice_emotion_model_path, voice_path);
    
    // 直接调用外部函数进行初始化
    LOG_INFO("准备调用外部函数: Java_com_my_emotionsdkdemo_voice_VoiceEmotionInterface_initVoiceModel");
    
    jstring result = nullptr;
    try {
        result = Java_com_my_emotionsdkdemo_voice_VoiceEmotionInterface_initVoiceModel(env, thiz, voice_emotion_model_path);
        LOG_INFO("✅ 外部函数调用完成，结果指针: %p", result);
        
        if (result != nullptr) {
            const char* result_str = env->GetStringUTFChars(result, 0);
            LOG_INFO("语音模型初始化结果: %s", result_str);
            env->ReleaseStringUTFChars(result, result_str);
        } else {
            LOG_ERROR("❌ 外部函数返回null");
        }
    } catch (...) {
        LOG_ERROR("❌ 调用外部函数时发生异常");
        return env->NewStringUTF("Error: Exception during external function call");
    }
    
    LOG_INFO("=== 语音情感模型初始化完成 ===");
    return result;
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_emotion_voice_sdk_VoiceEmotionInterface_detectVoice(
    JNIEnv *env, 
    jobject thiz,
    jfloatArray mfcc) {
    
    LOG_INFO("=== 开始语音情感检测 ===");
    LOG_DEBUG("JNI函数: Java_com_emotion_voice_sdk_VoiceEmotionInterface_detectVoice 被调用");
    
    // 记录开始时间
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 检查参数是否为空
    if (mfcc == nullptr) {
        LOG_ERROR("❌ mfcc 参数为空");
        return env->NewStringUTF("Error: mfcc is null");
    }
    
    // 检查数组长度
    jsize array_length = env->GetArrayLength(mfcc);
    LOG_INFO("MFCC数组参数检查:");
    LOG_INFO("- 数组长度: %d", array_length);
    LOG_INFO("- 期望长度: %d (48*39)", 48*39);
    
    if (array_length != 48*39) {
        LOG_ERROR("❌ MFCC数组长度不正确: %d, 期望: %d", array_length, 48*39);
        return env->NewStringUTF("Error: MFCC array length incorrect");
    }
    
    LOG_INFO("✅ MFCC数组参数验证通过");
    
    // 调用外部函数进行语音检测
    LOG_INFO("准备调用外部函数: Java_com_my_emotionsdkdemo_voice_VoiceEmotionInterface_detectVoice");
    
    // 记录模型推理开始时间
    auto inference_start = std::chrono::high_resolution_clock::now();
    
    jstring result = nullptr;
    try {
        result = Java_com_my_emotionsdkdemo_voice_VoiceEmotionInterface_detectVoice(env, thiz, mfcc);
        
        // 记录模型推理结束时间
        auto inference_end = std::chrono::high_resolution_clock::now();
        auto inference_duration = std::chrono::duration_cast<std::chrono::milliseconds>(inference_end - inference_start);
        
        LOG_INFO("✅ 外部函数调用完成，结果指针: %p", result);
        LOG_INFO("⏱️ 模型推理耗时: %lld 毫秒", inference_duration.count());
        
        if (result != nullptr) {
            const char* result_str = env->GetStringUTFChars(result, 0);
            LOG_INFO("语音检测结果: %s", result_str);
            env->ReleaseStringUTFChars(result, result_str);
        } else {
            LOG_ERROR("❌ 外部函数返回null");
        }
    } catch (...) {
        LOG_ERROR("❌ 调用外部函数时发生异常");
        return env->NewStringUTF("Error: Exception during external function call");
    }
    
    // 记录总耗时
    auto end_time = std::chrono::high_resolution_clock::now();
    auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    LOG_INFO("⏱️ 总处理耗时: %lld 毫秒", total_duration.count());
    LOG_INFO("=== 语音情感检测完成 ===");
    return result;
}

extern "C" JNIEXPORT void JNICALL
Java_com_emotion_voice_sdk_VoiceEmotionInterface_releaseVoiceModel(
    JNIEnv *env, 
    jobject thiz) {
    
    LOG_INFO("=== 开始释放语音模型资源 ===");
    LOG_DEBUG("JNI函数: Java_com_emotion_voice_sdk_VoiceEmotionInterface_releaseVoiceModel 被调用");
    
    try {
        Java_com_my_emotionsdkdemo_voice_VoiceEmotionInterface_releaseVoiceModel(env, thiz);
        LOG_INFO("✅ 语音模型资源释放完成");
    } catch (...) {
        LOG_ERROR("❌ 释放语音模型资源时发生异常");
    }
    
    LOG_INFO("=== 语音模型资源释放完成 ===");
}

 