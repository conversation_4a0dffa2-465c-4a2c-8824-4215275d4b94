package com.emotion.voice.sdk;

import android.util.Log;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 语音模块内存监控器
 * 
 * 功能：
 * - 监控VoiceSimpleBatchProcessor的池状态
 * - 提供语音模块专用的内存报告
 * - 语音模块自动优化建议
 */
public class VoiceMemoryMonitor {
    
    private static final String TAG = "VoiceMemoryMonitor";
    
    // 监控周期
    private static final long REPORT_INTERVAL = 30000; // 30秒
    
    // 统计信息
    private static final AtomicLong lastReportTime = new AtomicLong(0);
    private static final AtomicLong totalReports = new AtomicLong(0);
    
    /**
     * 生成语音模块内存报告
     */
    public static String generateMemoryReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 语音模块内存报告 ===\n");
        
        // 1. VoiceObjectPool池状态
        report.append("1. 语音处理池:\n");
        report.append("   ").append(VoiceObjectPool.getStats()).append("\n");
        
        // 2. 语音模块内存使用
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        report.append("2. 系统内存状态:\n");
        report.append("   使用内存: ").append(formatBytes(usedMemory)).append("\n");
        report.append("   内存使用率: ").append(String.format("%.1f%%", (double) usedMemory / maxMemory * 100)).append("\n");
        
        // 3. 语音模块优化建议
        report.append("3. 优化建议:\n");
        report.append(generateOptimizationAdvice());
        
        report.append("========================\n");
        
        totalReports.incrementAndGet();
        lastReportTime.set(System.currentTimeMillis());
        
        return report.toString();
    }
    
    /**
     * 生成语音模块优化建议
     */
    private static String generateOptimizationAdvice() {
        StringBuilder advice = new StringBuilder();
        
        // 检查内存使用率
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        double memoryUsage = (double) usedMemory / maxMemory;
        
        if (memoryUsage > 0.8) {
            advice.append("   - 内存使用率过高(").append(String.format("%.1f%%", memoryUsage * 100)).append(")，建议优化语音处理频率\n");
        } else if (memoryUsage > 0.6) {
            advice.append("   - 内存使用率较高(").append(String.format("%.1f%%", memoryUsage * 100)).append(")，注意监控\n");
        }
        
        // 检查语音池使用情况
        String poolStats = VoiceObjectPool.getStats();
        if (poolStats.contains("0.0%") || poolStats.contains("命中率较低")) {
            advice.append("   - 语音处理池命中率可能较低，考虑预热\n");
        }
        
        if (advice.length() == 0) {
            advice.append("   - 语音模块内存使用状况良好\n");
        }
        
        return advice.toString();
    }
    
    /**
     * 格式化字节数
     */
    private static String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * 语音模块定期监控检查
     */
    public static void performPeriodicCheck() {
        long currentTime = System.currentTimeMillis();
        long lastReport = lastReportTime.get();
        
        if (currentTime - lastReport >= REPORT_INTERVAL) {
            String report = generateMemoryReport();
            Log.i(TAG, report);
            
            // 检查是否需要优化
            if (shouldOptimizePools()) {
                optimizeVoicePools();
            }
        }
    }
    
    /**
     * 检查是否需要优化语音模块对象池
     */
    private static boolean shouldOptimizePools() {
        // 检查内存使用率
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        double memoryUsage = (double) usedMemory / maxMemory;
        
        return memoryUsage > 0.7; // 内存使用率超过70%时进行优化
    }
    
    /**
     * 优化语音模块对象池
     */
    public static void optimizeVoicePools() {
        Log.i(TAG, "开始优化语音模块对象池");
        
        // 优化语音处理池
        VoiceObjectPool.optimize();
        
        Log.i(TAG, "语音模块对象池优化完成");
    }
    
    /**
     * 预热语音模块对象池
     */
    public static void warmupVoicePools() {
        Log.i(TAG, "开始预热语音模块对象池");
        
        // 预热语音处理池
        VoiceObjectPool.warmup();
        
        Log.i(TAG, "语音模块对象池预热完成");
    }
    
    /**
     * 清理语音模块对象池
     */
    public static void cleanupVoicePools() {
        Log.i(TAG, "开始清理语音模块对象池");
        
        // 优化语音处理池（清理过多对象）
        VoiceObjectPool.optimize();
        
        Log.i(TAG, "语音模块对象池清理完成");
    }
    
    /**
     * 快速状态检查
     */
    public static String getQuickStatus() {
        return "语音模块内存状态: VoicePool=正常";
    }
    
    /**
     * 获取语音模块统计信息
     */
    public static String getStatistics() {
        return String.format("语音模块监控统计: 总报告=%d次, 最后报告=%d秒前", 
            totalReports.get(),
            (System.currentTimeMillis() - lastReportTime.get()) / 1000
        );
    }
    
    /**
     * 获取详细的语音模块状态
     */
    public static String getDetailedStatus() {
        return String.format(
            "=== 语音模块详细状态 ===\n" +
            "%s\n" +
            "%s\n" +
            "=========================",
            VoiceObjectPool.getStats(),
            getStatistics()
        );
    }
} 