package com.emotion.voice.sdk;

import android.content.Context;
import android.util.Log;

import com.konovalov.vad.silero.Vad;
import com.konovalov.vad.silero.VadSilero;
import com.konovalov.vad.silero.config.FrameSize;
import com.konovalov.vad.silero.config.Mode;
import com.konovalov.vad.silero.config.SampleRate;

import java.io.File;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;



public class VoiceEmotionModule {
    private static final String TAG = "VoiceEmotionModule";

    private final Context context;
    private final VoiceEmotionInterface emotionInterface;
    private final AudioProcessor audioProcessor;
    private final VoiceSimpleBatchProcessor batchProcessor;
    private final MfccExtractor mfccExtractor;
    private final int batchSize;

    private final AtomicBoolean isInitialized = new AtomicBoolean(false);
    private final AtomicBoolean isModelReady = new AtomicBoolean(false);
    private final AtomicBoolean isRunning = new AtomicBoolean(false);

    // 多线程VAD处理架构
    private volatile VadSilero vadSilero; // VAD实例，volatile确保可见性
    private static final int VAD_FRAME_SIZE_SAMPLES = 512; // Silero VAD推荐帧大小
    private final AtomicBoolean vadInitialized = new AtomicBoolean(false); // VAD初始化状态
    
    // 多线程缓冲区管理
    private final ThreadLocal<short[]> threadLocalVadFrame = new ThreadLocal<short[]>() {
        @Override
        protected short[] initialValue() {
            return new short[VAD_FRAME_SIZE_SAMPLES]; // 每线程独立缓冲区
        }
    };
    
    // VAD访问同步锁 - 多线程环境必需
    private final Object vadLock = new Object();

    // VAD 状态机参数
    private enum VadState { SILENCE, VOICE }
    private volatile VadState currentVadState = VadState.SILENCE;
    private int consecutiveVoiceFrames = 0;
    private int consecutiveSilenceFrames = 0;
    private final int MIN_VOICE_FRAMES = 15;      // 至少150ms连续语音
    private final int MIN_SILENCE_FRAMES = 30;    // 至少300ms连续静音

    // 定时器组件 - 优化为多线程处理
    private ScheduledExecutorService scheduler;
    private java.util.concurrent.ExecutorService processingExecutor;
    private ScheduledFuture<?> vadTask;
    private ScheduledFuture<?> emotionTask;
    
    // 性能优化：使用线程安全的性能计数器
    private final AtomicLong totalVadChecks = new AtomicLong(0);
    private final AtomicLong totalEmotionDetections = new AtomicLong(0);
    private final AtomicLong lastPerformanceReport = new AtomicLong(System.currentTimeMillis());
    
    public interface VoiceModuleCallback {
        void onInitializationSuccess();
        void onInitializationError(String error);
        void onDetectionStarted();
        void onDetectionStopped();
        void onFrameProcessed(String emotion, int frameIndex);
        void onBatchComplete(List<String> emotions, int batchIndex);
        void onError(String error);
        
        /**
         * VAD状态变化回调
         * @param hasVoice 是否有人声活动
         * @param stateDescription VAD状态描述
         */
        void onVadStateChanged(boolean hasVoice, String stateDescription);
    }
    private VoiceModuleCallback callback;

    public VoiceEmotionModule(Context context, int batchSize) {
        this.context = context;
        this.batchSize = batchSize;
        this.emotionInterface = new VoiceEmotionInterface();
        
        // 创建专门的音频处理器
        this.audioProcessor = new AudioProcessor(context);
        
        // 创建批次处理器
        this.batchProcessor = new VoiceSimpleBatchProcessor(batchSize);
        
        // 创建MFCC提取器
        this.mfccExtractor = new MfccExtractor(16000, 400, 40, 39);
        
        // 延迟初始化Silero VAD，在initialize()时创建，避免构造函数阻塞
        
        Log.i(TAG, "VoiceEmotionModule创建完成，批次大小: " + batchSize);
    }
    
    public void initialize() {
        if (isInitialized.get()) {
            Log.w(TAG, "模块已经初始化");
            return;
        }
        
        Log.i(TAG, "开始初始化语音情感检测模块");
        
        try {
            // 音频处理器诊断
            Log.i(TAG, "先进行音频设备诊断");
            AudioProcessor.diagnoseAudioDevice(context);
            
            // 初始化音频处理器
            if (audioProcessor.initializeAudioRecord()) {
                Log.i(TAG, "音频处理器初始化成功");
            } else {
                Log.e(TAG, "音频处理器初始化失败");
                if (callback != null) {
                    callback.onInitializationError("音频处理器初始化失败");
                }
                return;
            }
            
            // 复制和加载模型
            Log.i(TAG, "开始复制和加载语音模型...");
            String modelPath = copyModelToInternalStorage();
            if (modelPath != null) {
                Log.i(TAG, "模型文件复制成功: " + modelPath);
                
                // 在后台线程中加载模型，避免阻塞主线程
                new Thread(() -> {
                    try {
                        if (emotionInterface.initModel(modelPath)) {
                            Log.i(TAG, "情感识别模型加载成功");
                            isModelReady.set(true);
                            isInitialized.set(true);
                            
                            // 在主线程中调用回调
                            android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onInitializationSuccess();
                                }
                            });
                        } else {
                            Log.e(TAG, "情感识别模型加载失败");
                            isModelReady.set(false);
                            
                            android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onInitializationError("模型加载失败");
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "模型加载异常", e);
                        isModelReady.set(false);
                        
                        android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onInitializationError("模型加载异常: " + e.getMessage());
                            }
                        });
                    }
                }, "ModelLoader").start();
                
            } else {
                Log.e(TAG, "模型文件复制失败");
                if (callback != null) {
                    callback.onInitializationError("模型文件复制失败");
                }
                return;
            }
            
            // 配置批次处理器回调
            batchProcessor.setCallback(new VoiceSimpleBatchProcessor.BatchProcessorCallback() {
                public void onBatchComplete(List<String> emotions, int batchIndex) {
                    if (callback != null) {
                        callback.onBatchComplete(emotions, batchIndex);
                    }
                }
                
                public void onProcessorError(String error) {
                    if (callback != null) {
                        callback.onError("批次处理器错误: " + error);
                    }
                }
                
                public void onPerformanceUpdate(String performanceReport) {
                    Log.d(TAG, "批次处理器性能: " + performanceReport);
                }
            });
            
            // 在模型加载成功后初始化Silero VAD
            initSileroVadSafely();
            
            Log.i(TAG, "语音情感检测模块组件初始化完成，等待模型加载");
            
        } catch (Exception e) {
            isInitialized.set(false);
            Log.e(TAG, "模块初始化异常", e);
            if (callback != null) callback.onInitializationError("初始化异常: " + e.getMessage());
        }
    }
    
    public boolean startDetection() {
        if (!isInitialized.get()) {
            Log.w(TAG, "模块未初始化，请先调用initialize()");
            return false;
        }
        if (isRunning.get()) return true;
        
        // 重置VAD学习和状态
        resetVadState();
        
        // 创建多线程线程池
        scheduler = Executors.newScheduledThreadPool(2); // VAD + 情感检测调度
        processingExecutor = Executors.newFixedThreadPool(2); // 保持多线程处理，通过同步确保线程安全
        
        isRunning.set(true);
        audioProcessor.startAudioProcessing();
        
        // 启动0.5秒一次的VAD任务
        vadTask = scheduler.scheduleAtFixedRate(this::runVadCheckAsync, 0, 500, TimeUnit.MILLISECONDS);
        
        if (callback != null) callback.onDetectionStarted();
        Log.i(TAG, "语音检测已启动（多线程异步模式）");
        return true;
    }
    
    private void resetVadState() {
        this.currentVadState = VadState.SILENCE;
        this.consecutiveVoiceFrames = 0;
        this.consecutiveSilenceFrames = 0;
        this.totalVadChecks.set(0);
        this.totalEmotionDetections.set(0);
        this.lastPerformanceReport.set(System.currentTimeMillis());
    }
    
    private void runVadCheckAsync() {
        //异步执行：限制并发数，防止任务堆积
        if (processingExecutor != null && !processingExecutor.isShutdown()) {
            try {
                // 非阻塞提交，如果线程池满了就跳过这次检测
                processingExecutor.submit(() -> {
                    try {
                        totalVadChecks.incrementAndGet();
                        performVadCheckSafely(); // 使用线程安全版本
                    } catch (Exception e) {
                        Log.e(TAG, "VAD检测任务异常", e);
                    }
                });
            } catch (Exception e) {
                Log.w(TAG, "提交VAD任务失败，跳过此次检测", e);
            }
        }
    }
    
    /**
     * 线程安全的VAD检测方法
     */
    private void performVadCheckSafely() {
        short[] audioChunk = null;
        try {
            // 获取音频数据
            audioChunk = VoiceObjectPool.acquireAudioBuffer();
            if (audioChunk == null) {
                Log.w(TAG, "无法获取音频缓冲区，跳过此次VAD检测");
                return;
            }
            
            audioProcessor.getRecentAudioChunk(500, audioChunk);
            
            // 线程安全地处理音频帧
            processAudioFramesSafely(audioChunk);
            
            // 定期输出性能报告
            reportPerformanceIfNeeded();
            
        } catch (Exception e) {
            Log.e(TAG, "VAD检测异常", e);
        } finally {
            // 确保资源回收
            if (audioChunk != null) {
                try {
                    VoiceObjectPool.releaseAudioBuffer(audioChunk);
                } catch (Exception e) {
                    Log.w(TAG, "回收音频缓冲区异常", e);
                }
            }
        }
    }
    
    /**
     * 多线程音频帧处理
     */
    private void processAudioFramesSafely(short[] audioChunk) {
        final int frameStep = VAD_FRAME_SIZE_SAMPLES;
        
        // 内存安全检查
        if (audioChunk == null || audioChunk.length < frameStep) {
            return;
        }
        
        // 获取当前线程的独立缓冲区
        short[] vadFrame = threadLocalVadFrame.get();
        if (vadFrame == null || vadFrame.length != VAD_FRAME_SIZE_SAMPLES) {
            Log.w(TAG, "线程本地缓冲区异常，跳过此次检测");
            return;
        }
        
        // 遍历0.5秒的音频数据
        for (int i = 0; i <= audioChunk.length - frameStep; i += frameStep) {
            try {
                // 使用线程独立的缓冲区，多线程环境安全
                System.arraycopy(audioChunk, i, vadFrame, 0, frameStep);
                
                // 多线程安全的VAD检测
                boolean hasVoice = isSileroVoiceFrameThreadSafe(vadFrame);
                
                // 多线程状态机更新（状态机方法内部有同步）
                updateVadState(hasVoice);
                
            } catch (Exception e) {
                Log.e(TAG, "处理音频帧异常，跳过此帧", e);
                // 继续处理下一帧，不中断整个检测流程
            }
        }
    }
    
    private synchronized void startEmotionDetectionTask() {
        if (emotionTask == null || emotionTask.isDone()) {
            emotionTask = scheduler.scheduleAtFixedRate(this::runEmotionDetectionAsync, 0, 500, TimeUnit.MILLISECONDS);
            Log.i(TAG, "检测到人声，启动情感识别定时器");
        }
    }
    
    private synchronized void stopEmotionDetectionTask() {
        if (emotionTask != null && !emotionTask.isDone()) {
            emotionTask.cancel(false);
            emotionTask = null;
            Log.i(TAG, "静音，停止情感识别定时器");
        }
    }
    
    private void runEmotionDetectionAsync() {
        // 异步执行情感检测，避免阻塞调度器
        processingExecutor.submit(() -> {
            try {
                totalEmotionDetections.incrementAndGet();
                performEmotionDetection();
            } catch (Exception e) {
                Log.e(TAG, "情感识别任务异常", e);
            }
        });
    }
    
    private void performEmotionDetection() {
        if (!isModelReady.get()) return;
        
        float[] features = null;
        try {
            short[] audioChunk = VoiceObjectPool.acquireAudioBuffer();
            audioProcessor.getRecentAudioChunk(500, audioChunk);
            
            features = mfccExtractor.extractFeaturesFromChunk(audioChunk, 48, (8000 - 400) / 47);
            String emotion = emotionInterface.detectVoiceSafe(features);
            
            if (emotion != null && !emotion.isEmpty()) {
                // 回调单次检测结果
                if (callback != null) {
                    callback.onFrameProcessed(emotion, 0);
                }
                
                // 异步处理批次结果
                processingExecutor.submit(() -> batchProcessor.processEmotion(emotion));
            }
            
            VoiceObjectPool.releaseAudioBuffer(audioChunk);
        } catch (Exception e) {
            Log.e(TAG, "情感识别异常", e);
        } finally {
            if (features != null) {
                VoiceObjectPool.releaseMfccFeatures(features);
            }
        }
    }
    
    private void reportPerformanceIfNeeded() {
        long currentTime = System.currentTimeMillis();
        long lastReport = lastPerformanceReport.get();
        
        if (currentTime - lastReport > 5000) { // 5秒报告一次
            if (lastPerformanceReport.compareAndSet(lastReport, currentTime)) {
                long vadCount = totalVadChecks.get();
                long emotionCount = totalEmotionDetections.get();
                
                Log.i(TAG, "语音检测性能统计:");
                Log.i(TAG, "  VAD检查次数: " + vadCount);
                Log.i(TAG, "  情感检测次数: " + emotionCount);
                Log.i(TAG, "  VAD频率: " + (vadCount / 5.0) + " Hz");
                Log.i(TAG, "  情感检测频率: " + (emotionCount / 5.0) + " Hz");
                
                // 重置计数器
                totalVadChecks.set(0);
                totalEmotionDetections.set(0);
            }
        }
    }
    
    public void stopDetection() {
        if (!isRunning.getAndSet(false)) return;
        
        audioProcessor.stopAudioProcessing();
        
        // 关闭所有线程池
        if (scheduler != null) {
            scheduler.shutdownNow();
            scheduler = null;
        }
        
        if (processingExecutor != null) {
            processingExecutor.shutdownNow();
            processingExecutor = null;
        }
        
        batchProcessor.forceCompleteBatch();
        
        if (callback != null) callback.onDetectionStopped();
        Log.i(TAG, "语音检测已停止（多线程资源已释放）");
    }
    
    public void release() {
        try {
            // 按顺序安全释放所有资源
            stopDetection();
            
            // 释放VAD资源
            releaseSileroVadSafely();
            
            // 释放音频处理器
            if (audioProcessor != null) {
                audioProcessor.release();
            }
            
            // 释放情感检测模型
            if (emotionInterface != null) {
                emotionInterface.releaseModelSafe();
            }
            
            Log.i(TAG, "✅ 语音模块所有资源已安全释放");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ 释放资源时发生异常", e);
            // 即使出现异常也要确保基本清理
            vadSilero = null;
            vadInitialized.set(false);
        }
    }
    
    public void setCallback(VoiceModuleCallback callback) {
        this.callback = callback;
    }
    
    // VAD内存管理方法
    
    /**
     * 安全初始化Silero VAD，带完整异常处理
     */
    private void initSileroVadSafely() {
        // 防止重复初始化
        if (vadInitialized.get() && vadSilero != null) {
            Log.d(TAG, "Silero VAD已经初始化，跳过");
            return;
        }
        
        try {
            Log.i(TAG, "开始初始化Silero VAD...");
            
            // 使用Builder模式创建VAD实例
            VadSilero tempVad = Vad.builder()
                    .setContext(context)
                    .setSampleRate(SampleRate.SAMPLE_RATE_16K)
                    .setFrameSize(FrameSize.FRAME_SIZE_512)
                    .setMode(Mode.NORMAL) // 平衡准确性和性能
                    .setSilenceDurationMs(300)
                    .setSpeechDurationMs(50)
                    .build();
            
            // 原子性地设置VAD实例
            vadSilero = tempVad;
            vadInitialized.set(true);
            
            Log.i(TAG, "✅ Silero VAD初始化成功，内存管理就绪");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Silero VAD初始化失败", e);
            vadSilero = null;
            vadInitialized.set(false);
        }
    }
    
    /**
     * 线程安全的VAD检测方法 - 同步访问VAD实例
     */
    private boolean isSileroVoiceFrameThreadSafe(short[] audioFrame) {
        // 多重安全检查
        if (audioFrame == null || audioFrame.length != VAD_FRAME_SIZE_SAMPLES) {
            return false;
        }
        
        // 检查VAD实例状态
        if (!vadInitialized.get()) {
            return false;
        }
        
        // 同步访问VAD实例，确保线程安全
        synchronized (vadLock) {
            final VadSilero currentVad = vadSilero;
            if (currentVad == null) {
                return false;
            }
            
            try {
                // 单线程访问Silero VAD，避免并发问题
                boolean hasVoice = currentVad.isSpeech(audioFrame);
                
                if (Log.isLoggable(TAG, Log.VERBOSE) && hasVoice) {
                    Log.v(TAG, "Silero VAD: 检测到人声 [线程:" + Thread.currentThread().getName() + "]");
                }
                
                return hasVoice;
                
            } catch (Exception e) {
                Log.e(TAG, "Silero VAD检测异常，返回静音", e);
                return false; // 异常时返回安全值
            }
        }
    }
    
    /**
     *多线程安全的Silero VAD资源释放
     */
    private void releaseSileroVadSafely() {
        if (!vadInitialized.getAndSet(false)) {
            Log.d(TAG, "VAD未初始化，跳过释放");
            return;
        }
        
        //  多线程同步释放，确保没有线程正在使用VAD
        synchronized (vadLock) {
            final VadSilero vadToRelease = vadSilero;
            vadSilero = null; // 立即设为null，防止新的访问
            
            if (vadToRelease != null) {
                try {
                    Log.i(TAG, "开始多线程安全释放Silero VAD资源...");
                    vadToRelease.close();
                    Log.i(TAG, "✅ Silero VAD资源释放成功");
                } catch (Exception e) {
                    Log.e(TAG, "❌ 释放Silero VAD异常", e);
                }
            }
        }
        
        // 清理所有线程的本地存储
        try {
            threadLocalVadFrame.remove();
            Log.d(TAG, "当前线程的VAD缓冲区已清理");
        } catch (Exception e) {
            Log.w(TAG, "清理线程本地缓冲区异常", e);
        }
    }
    
    private void updateVadState(boolean hasVoice) {
        VadState previousState = currentVadState;
        
        if (hasVoice) {
            consecutiveVoiceFrames++;
            consecutiveSilenceFrames = 0;
            
            if (currentVadState == VadState.SILENCE && consecutiveVoiceFrames >= MIN_VOICE_FRAMES) {
                currentVadState = VadState.VOICE;
                Log.d(TAG, "VAD状态切换: 静音 → 语音");
                startEmotionDetectionTask();
            }
        } else {
            consecutiveSilenceFrames++;
            consecutiveVoiceFrames = 0;
            
            if (currentVadState == VadState.VOICE && consecutiveSilenceFrames >= MIN_SILENCE_FRAMES) {
                currentVadState = VadState.SILENCE;
                Log.d(TAG, "VAD状态切换: 语音 → 静音");
                stopEmotionDetectionTask();
            }
        }
        
        // 如果VAD状态发生变化，触发回调
        if (previousState != currentVadState && callback != null) {
            String stateDescription = getVadStateDescription();
            callback.onVadStateChanged(currentVadState == VadState.VOICE, stateDescription);
        }
    }
    
    /**
     * 获取当前VAD状态
     * @return true表示有人声活动，false表示静音
     */
    public boolean hasVoiceActivity() {
        return currentVadState == VadState.VOICE;
    }
    
    /**
     * 获取详细的VAD状态信息
     * @return VAD状态描述字符串
     */
    public String getVadStateDescription() {
        return String.format("VAD状态: %s, 连续语音帧: %d, 连续静音帧: %d", 
                currentVadState == VadState.VOICE ? "语音" : "静音",
                consecutiveVoiceFrames, consecutiveSilenceFrames);
    }
    
    // 内部方法
    private String copyModelToInternalStorage() {
        try {
            String modelFilename = "10-fold_weights_best_1_1x48x39_rk3588.rknn";
            String internalPath = context.getFilesDir().getAbsolutePath() + "/models/" + modelFilename;
            File internalFile = new File(internalPath);
            
            // 如果文件已存在且大小正确，直接返回
            if (internalFile.exists() && internalFile.length() > 0) {
                Log.i(TAG, "模型文件已存在，路径: " + internalPath);
                return internalPath;
            }
            
            // 创建目录
            File parentDir = internalFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 复制文件
            try (java.io.InputStream inputStream = context.getAssets().open("models/" + modelFilename);
                 java.io.FileOutputStream outputStream = new java.io.FileOutputStream(internalFile)) {
                
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                
                Log.i(TAG, "模型文件复制成功: " + internalPath);
                return internalPath;
            }
            
        } catch (Exception e) {
            Log.e(TAG, "复制模型文件失败", e);
            return null;
        }
    }
} 