package com.emotion.voice.sdk;

import android.util.Log;

/**
 * VAD (Voice Activity Detection) Configuration - 自定义VAD版
 * 为我们自定义的状态机和底层的自定义VAD算法提供配置参数
 */
public class VADConfiguration {
    private static final String TAG = "VADConfiguration";
    private static volatile VADConfiguration instance;

    // 参数1: 自定义VAD的激进模式 (0-3, 保留参数用于扩展)
    private int aggressivenessMode = 3; // 默认：最激进

    // 参数2 & 3: 我们自定义状态机的参数
    private int minVoiceDurationMs = 100; // 至少100ms连续人声才确认为语音开始
    private int trailingSilenceDurationMs = 500; // 语音结束后，允许500ms的尾随静音

    private VADConfiguration() {}

    public static VADConfiguration getInstance() {
        if (instance == null) {
            synchronized (VADConfiguration.class) {
                if (instance == null) {
                    instance = new VADConfiguration();
                }
            }
        }
        return instance;
    }

    // --- Getter and Setter Methods ---

    public int getAggressivenessMode() { return aggressivenessMode; }
    public VADConfiguration setAggressivenessMode(int mode) {
        this.aggressivenessMode = Math.max(0, Math.min(3, mode));
        return this;
    }

    public int getMinVoiceDurationMs() { return minVoiceDurationMs; }
    public VADConfiguration setMinVoiceDurationMs(int ms) {
        this.minVoiceDurationMs = Math.max(0, ms);
        return this;
    }

    public int getTrailingSilenceDurationMs() { return trailingSilenceDurationMs; }
    public VADConfiguration setTrailingSilenceDurationMs(int ms) {
        this.trailingSilenceDurationMs = Math.max(0, ms);
        return this;
    }
} 