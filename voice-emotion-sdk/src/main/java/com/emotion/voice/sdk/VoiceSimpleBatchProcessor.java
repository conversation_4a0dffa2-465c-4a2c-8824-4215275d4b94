package com.emotion.voice.sdk;

import android.util.Log;
import java.util.List;
import java.util.ArrayList;

public class VoiceSimpleBatchProcessor {
    private static final String TAG = "VoiceBatchProcessor";

    private final int copyCount;
    private final List<String> batchBuffer; // 用于暂存2个结果

    private BatchProcessorCallback callback;

    public interface BatchProcessorCallback {
        void onBatchComplete(List<String> emotions, int batchIndex);
        void onProcessorError(String error);
    }

    public VoiceSimpleBatchProcessor(int copyCount) {
        this.copyCount = Math.max(1, copyCount);
        this.batchBuffer = new ArrayList<>(2);
        Log.i(TAG, "批次处理器创建，复制次数: " + this.copyCount);
    }

    public void setCallback(BatchProcessorCallback callback) { this.callback = callback; }

    public synchronized void processEmotion(String emotion) {
        if (emotion == null || emotion.isEmpty()) return;
        
        batchBuffer.add(emotion);

        // 当收集到2个情感结果时，触发批次生成逻辑
        if (batchBuffer.size() >= 2) {
            triggerBatchComplete();
        }
    }

    private void triggerBatchComplete() {
        if (callback == null) {
            batchBuffer.clear(); // 如果没有回调，直接清空
            return;
        }
        
        // 从对象池获取最终用于回调的列表
        List<String> finalBatch = VoiceObjectPool.acquireStringList();
        
        // 执行"复制N次"的逻辑
        for (int i = 0; i < this.copyCount; i++) {
            finalBatch.addAll(this.batchBuffer);
        }

        Log.i(TAG, String.format("批次完成: 2个源结果被复制%d次，生成%d个结果", copyCount, finalBatch.size()));

        // 触发回调
        try {
            callback.onBatchComplete(finalBatch, 0); // batchIndex可以简化
        } finally {
            // 回调完成后，立即回收列表
            VoiceObjectPool.releaseStringList(finalBatch);
        }
        
        // 清空缓冲区，为下2个结果做准备
        batchBuffer.clear();
    }

    public synchronized void forceCompleteBatch() {
        // 在这种模式下，强制完成意义不大，因为需要2个结果才能生成批次
        // 但为了完整性，可以清空缓冲区
        if (!batchBuffer.isEmpty()) {
            Log.w(TAG, "强制完成批次，但缓冲区未满(需要2个)，清空缓冲区。");
            batchBuffer.clear();
        }
    }
    
    public void reset() {
        batchBuffer.clear();
    }
    
    public void release() {
        reset();
    }
} 