package com.emotion.voice.sdk;

import android.util.Log;
import org.jtransforms.fft.DoubleFFT_1D;

/**
 * MFCC特征提取器
 * 提供专业的梅尔频率倒谱系数计算
 * 
 * 重构升级：
 * 1. 单帧MFCC提取（原有功能）
 * 2. 批量MFCC提取：直接从0.5秒音频生成48*39=1872维特征
 */
public class MfccExtractor {
    
    private static final String TAG = "MfccExtractor";
    
    private final int sampleRate;
    private final int fftSize;
    private final int melFilters;
    private final int mfccCoefficients;
    
    // JTransforms FFT对象
    private final DoubleFFT_1D fftTransform;
    
    // FFT相关
    private final double[] window;
    private final double[] fftBuffer;
    private final double[][] melFilterBank;
    private final double[] melEnergies;
    private final double[] logMelEnergies;
    private final double[][] dctMatrix;
    
    // 复用的缓冲区 - 避免重复创建
    private final float[] preEmphasisBuffer;
    private final double[] powerSpectrumBuffer;
    private final float[] mfccBuffer;
    
    // 预计算的常量
    private final double melLowFreq = 0;
    private final double melHighFreq;
    private final double freqStep;
    
    public MfccExtractor(int sampleRate, int fftSize, int melFilters, int mfccCoefficients) {
        this.sampleRate = sampleRate;
        this.fftSize = fftSize;
        this.melFilters = melFilters;
        this.mfccCoefficients = mfccCoefficients;
        
        this.melHighFreq = hzToMel(sampleRate / 2.0);
        this.freqStep = (double) sampleRate / fftSize;
        
        // 初始化JTransforms FFT
        this.fftTransform = new DoubleFFT_1D(fftSize);
        
        // 初始化缓冲区
        this.window = new double[fftSize];

        this.fftBuffer = new double[fftSize * 2]; // 复数FFT
        this.melEnergies = new double[melFilters];
        this.logMelEnergies = new double[melFilters];
        
        // 初始化复用缓冲区
        this.preEmphasisBuffer = new float[fftSize];
        this.powerSpectrumBuffer = new double[fftSize / 2 + 1];
        this.mfccBuffer = new float[mfccCoefficients];
        
        // 预计算窗函数
        initializeWindow();
        
        // 预计算梅尔滤波器组
        this.melFilterBank = createMelFilterBank();
        
        // 预计算DCT矩阵
        this.dctMatrix = createDctMatrix();
    }
    
    private void initializeWindow() {
        // 汉明窗
        for (int i = 0; i < fftSize; i++) {
            window[i] = 0.54 - 0.46 * Math.cos(2.0 * Math.PI * i / (fftSize - 1));
        }
    }
    
    private double[][] createMelFilterBank() {
        double[][] filterBank = new double[melFilters][fftSize / 2 + 1];
        
        // 计算梅尔刻度上的等间距点
        double[] melPoints = new double[melFilters + 2];
        double melStep = (melHighFreq - melLowFreq) / (melFilters + 1);
        
        for (int i = 0; i < melPoints.length; i++) {
            melPoints[i] = melLowFreq + i * melStep;
        }
        
        // 转换回赫兹
        double[] hzPoints = new double[melPoints.length];
        for (int i = 0; i < melPoints.length; i++) {
            hzPoints[i] = melToHz(melPoints[i]);
        }
        
        // 转换为FFT bin索引
        int[] binPoints = new int[hzPoints.length];
        for (int i = 0; i < hzPoints.length; i++) {
            binPoints[i] = (int) Math.floor(hzPoints[i] / freqStep);
        }
        
        // 创建三角滤波器
        for (int m = 1; m <= melFilters; m++) {
            int leftBin = binPoints[m - 1];
            int centerBin = binPoints[m];
            int rightBin = binPoints[m + 1];
            
            // 左侧上升斜率
            for (int k = leftBin; k < centerBin; k++) {
                if (k >= 0 && k < filterBank[m - 1].length) {
                    filterBank[m - 1][k] = (double) (k - leftBin) / (centerBin - leftBin);
                }
            }
            
            // 右侧下降斜率
            for (int k = centerBin; k < rightBin; k++) {
                if (k >= 0 && k < filterBank[m - 1].length) {
                    filterBank[m - 1][k] = (double) (rightBin - k) / (rightBin - centerBin);
                }
            }
        }
        
        return filterBank;
    }
    
    private double[][] createDctMatrix() {
        double[][] dct = new double[mfccCoefficients][melFilters];
        
        for (int i = 0; i < mfccCoefficients; i++) {
            for (int j = 0; j < melFilters; j++) {
                dct[i][j] = Math.sqrt(2.0 / melFilters) * 
                    Math.cos(Math.PI * i * (j + 0.5) / melFilters);
            }
        }
        
        return dct;
    }
    
    /**
     * 提取单帧MFCC特征（私有方法）
     */
    private float[] extractMfccForSingleFrame(float[] audioFrame) {
        if (audioFrame == null || audioFrame.length < fftSize) {
            return null;
        }
        
        try {
            // 1. 预加重
            float[] preemphasized = preEmphasis(audioFrame);
            
            // 2. 加窗
            applyWindow(preemphasized);
            
            // 3. FFT
            double[] powerSpectrum = computePowerSpectrum(preemphasized);
            
            // 4. 梅尔滤波器组
            applyMelFilterBank(powerSpectrum);
            
            // 5. 对数变换
            applyLogTransform();
            
            // 6. DCT变换
            float[] mfccCoefficients = applyDct();
            
            return mfccCoefficients;
            
        } catch (Exception e) {
            Log.e(TAG, "Error extracting MFCC", e);
            return null;
        }
    }
    
    private float[] preEmphasis(float[] signal) {
        float alpha = 0.97f;
        int len = Math.min(signal.length, preEmphasisBuffer.length);
        
        preEmphasisBuffer[0] = signal[0];
        for (int i = 1; i < len; i++) {
            preEmphasisBuffer[i] = signal[i] - alpha * signal[i - 1];
        }
        
        return preEmphasisBuffer;
    }
    
    private void applyWindow(float[] signal) {
        int len = Math.min(signal.length, fftSize);
        for (int i = 0; i < len; i++) {
            signal[i] *= window[i];
        }
    }
    
    private double[] computePowerSpectrum(float[] signal) {
        // 准备FFT输入 - JTransforms需要交错格式的复数数组
        for (int i = 0; i < fftSize; i++) {
            if (i < signal.length) {
                fftBuffer[i * 2] = signal[i];     // 实部
                fftBuffer[i * 2 + 1] = 0;         // 虚部
            } else {
                fftBuffer[i * 2] = 0;
                fftBuffer[i * 2 + 1] = 0;
            }
        }
        
        // 使用JTransforms执行高性能FFT
        fftTransform.complexForward(fftBuffer);
        
        // 计算功率谱
        for (int i = 0; i < powerSpectrumBuffer.length; i++) {
            double real = fftBuffer[i * 2];
            double imag = fftBuffer[i * 2 + 1];
            powerSpectrumBuffer[i] = real * real + imag * imag;
        }
        
        return powerSpectrumBuffer;
    }
    
    private void applyMelFilterBank(double[] powerSpectrum) {
        for (int i = 0; i < melFilters; i++) {
            melEnergies[i] = 0;
            for (int j = 0; j < powerSpectrum.length; j++) {
                melEnergies[i] += powerSpectrum[j] * melFilterBank[i][j];
            }
        }
    }
    
    private void applyLogTransform() {
        for (int i = 0; i < melFilters; i++) {
            logMelEnergies[i] = Math.log(Math.max(melEnergies[i], 1e-10));
        }
    }
    
    private float[] applyDct() {
        // 使用复用的MFCC缓冲区
        for (int i = 0; i < mfccCoefficients; i++) {
            mfccBuffer[i] = 0;
            for (int j = 0; j < melFilters; j++) {
                mfccBuffer[i] += logMelEnergies[j] * dctMatrix[i][j];
            }
        }
        
        return mfccBuffer;
    }
    
    private double hzToMel(double hz) {
        return 2595.0 * Math.log10(1.0 + hz / 700.0);
    }
    
    private double melToHz(double mel) {
        return 700.0 * (Math.pow(10.0, mel / 2595.0) - 1.0);
    }
    
    // ================= 音频块MFCC提取方法 =================
    
    /**
     * 从音频块提取特征 - 新核心方法
     * @param audioChunk 音频块数据
     * @param frameCount 要提取的帧数（例如48帧）
     * @param frameHop 帧移（采样点数）
     * @return 完整的MFCC特征向量（从对象池获取）
     */
    public float[] extractFeaturesFromChunk(short[] audioChunk, int frameCount, int frameHop) {
        if (audioChunk == null || audioChunk.length == 0) {
            Log.e(TAG, "音频块为空");
            return null;
        }
        
        try {
            // 1. 从VoiceObjectPool获取一个用于存储最终结果的大数组
            float[] finalFeatures = VoiceObjectPool.acquireMfccFeatures();
            if (finalFeatures == null || finalFeatures.length < frameCount * mfccCoefficients) {
                // 池中没有合适大小的数组，创建新的
                finalFeatures = new float[frameCount * mfccCoefficients];
                Log.d(TAG, "创建新的特征数组: " + finalFeatures.length + "维");
            }
            
            // 2. 写一个循环，迭代frameCount次（例如48次）
            for (int frame = 0; frame < frameCount; frame++) {
                // 3. 根据帧移frameHop从audioChunk中切割出一个长度为fftSize的小音频帧
                int frameStart = frame * frameHop;
                short[] frameShortAudio = new short[fftSize];
                
                // 从audioChunk中复制数据，处理边界情况
                for (int i = 0; i < fftSize; i++) {
                    int audioIndex = frameStart + i;
                    if (audioIndex >= 0 && audioIndex < audioChunk.length) {
                        frameShortAudio[i] = audioChunk[audioIndex];
                    } else {
                        frameShortAudio[i] = 0; // 零填充
                    }
                }
                
                // 4. 将这个short[]类型的小音频帧转换为float[]类型
                float[] frameFloatAudio = new float[fftSize];
                for (int i = 0; i < fftSize; i++) {
                    frameFloatAudio[i] = frameShortAudio[i] / 32768.0f; // 归一化到[-1, 1]
                }
                
                // 5. 调用私有的extractMfccForSingleFrame()方法计算这一帧的MFCC特征
                float[] frameMfccFeatures = extractMfccForSingleFrame(frameFloatAudio);
                
                // 6. 使用System.arraycopy()将计算出的MFCC特征向量复制到finalFeatures大数组的正确位置
                if (frameMfccFeatures != null && frameMfccFeatures.length == mfccCoefficients) {
                    int destPosition = frame * mfccCoefficients;
                    System.arraycopy(frameMfccFeatures, 0, finalFeatures, destPosition, mfccCoefficients);
                } else {
                    // 如果特征提取失败，填充零值
                    int destPosition = frame * mfccCoefficients;
                    for (int i = 0; i < mfccCoefficients; i++) {
                        finalFeatures[destPosition + i] = 0.0f;
                    }
                    // 静默处理特征提取失败，避免频繁警告日志
                }
            }
            
            // 7. 循环结束后，返回finalFeatures
            // 静默特征提取完成，不输出日志避免频繁打印
            
            return finalFeatures;
            
        } catch (Exception e) {
            Log.e(TAG, "音频块特征提取异常", e);
            return null;
        }
    }
    
    /**
     * 为音频块提取特征 - 核心方法
     * 将传入的音频块分割成48个有重叠的帧，并提取完整的MFCC特征
     * 
     * @param audioChunk 音频块数据（例如0.5秒的音频数据）
     * @return 1872维的MFCC特征向量 (48帧 * 39系数)，如果失败返回null
     */
    public float[] extractFeaturesForChunk(short[] audioChunk) {
        if (audioChunk == null || audioChunk.length == 0) {
            Log.e(TAG, "音频块为空");
            return null;
        }
        
        try {
            // 转换short[]到float[]
            float[] floatAudio = convertToFloat(audioChunk);
            
            // 实现分帧逻辑并提取48帧MFCC特征
            return extractFramedMfccFeatures(floatAudio, 48);
            
        } catch (Exception e) {
            Log.e(TAG, "音频块特征提取失败", e);
            return null;
        }
    }
    
    /**
     * 提取分帧MFCC特征 - 核心分帧逻辑实现
     * 
     * @param audioData 浮点音频数据
     * @param frameCount 要提取的帧数（通常为48）
     * @return 连接后的MFCC特征向量（从对象池获取）
     */
    private float[] extractFramedMfccFeatures(float[] audioData, int frameCount) {
        // 计算帧参数 - 有重叠的分帧
        int hopLength = calculateHopLength(audioData.length, frameCount);
        
        // 从对象池获取结果数组
        float[] resultFeatures = VoiceObjectPool.acquireMfccFeatures();
        if (resultFeatures == null || resultFeatures.length < frameCount * mfccCoefficients) {
            // 池中没有合适大小的数组，创建新的
            resultFeatures = new float[frameCount * mfccCoefficients];
        }
        
        int resultOffset = 0;
        
        // 逐帧提取MFCC
        for (int frame = 0; frame < frameCount; frame++) {
            int frameStart = frame * hopLength;
            
            // 提取当前帧的音频数据
            float[] frameAudio = extractFrameData(audioData, frameStart, fftSize);
            
            // 调用已有的extractMfccForSingleFrame方法计算MFCC特征
            float[] frameMfcc = extractMfccForSingleFrame(frameAudio);
            
            if (frameMfcc != null && frameMfcc.length == mfccCoefficients) {
                // 复制到结果数组
                System.arraycopy(frameMfcc, 0, resultFeatures, resultOffset, mfccCoefficients);
            } else {
                // 如果提取失败，填充零值
                for (int i = 0; i < mfccCoefficients; i++) {
                    resultFeatures[resultOffset + i] = 0.0f;
                }
            }
            
            resultOffset += mfccCoefficients;
        }
        
        // 静默MFCC提取完成，避免频繁日志输出
        
        return resultFeatures;
    }
    
    /**
     * 计算帧间距 - 确保有重叠的分帧
     */
    private int calculateHopLength(int audioLength, int frameCount) {
        if (frameCount <= 1) {
            return audioLength;
        }
        
        // 计算理想的帧间距，确保覆盖整个音频
        int idealHopLength = (audioLength - fftSize) / (frameCount - 1);
        
        // 确保至少有一些重叠
        int minHopLength = fftSize / 4; // 75%重叠
        int maxHopLength = fftSize * 3 / 4; // 25%重叠
        
        return Math.max(minHopLength, Math.min(idealHopLength, maxHopLength));
    }
    
    /**
     * 提取指定位置的帧数据 - 处理边界情况
     */
    private float[] extractFrameData(float[] audioData, int frameStart, int frameLength) {
        float[] frameData = new float[frameLength];
        
        for (int i = 0; i < frameLength; i++) {
            int audioIndex = frameStart + i;
            if (audioIndex >= 0 && audioIndex < audioData.length) {
                frameData[i] = audioData[audioIndex];
            } else {
                frameData[i] = 0.0f; // 零填充
            }
        }
        
        return frameData;
    }
    

    
    /**
     * 将short数组转换为float数组
     */
    private float[] convertToFloat(short[] shortAudio) {
        float[] floatAudio = new float[shortAudio.length];
        
        for (int i = 0; i < shortAudio.length; i++) {
            floatAudio[i] = shortAudio[i] / 32768.0f; // 归一化到[-1, 1]
        }
        
        return floatAudio;
    }
    
    /**
     * 获取音频块特征的预期维度
     */
    public int getChunkFeatureDimensions() {
        return 48 * mfccCoefficients; // 48帧 × 39系数 = 1872维
    }
    
    /**
     * 验证音频块特征向量的有效性
     */
    public boolean validateChunkFeatures(float[] chunkFeatures) {
        if (chunkFeatures == null) return false;
        if (chunkFeatures.length != getChunkFeatureDimensions()) return false;
        
        // 检查是否包含有效数值（非全零、非NaN、非无穷）
        boolean hasValidData = false;
        for (float value : chunkFeatures) {
            if (Float.isNaN(value) || Float.isInfinite(value)) {
                return false;
            }
            if (value != 0.0f) {
                hasValidData = true;
            }
        }
        
        return hasValidData;
    }
    
    /**
     * 释放特征数组到对象池
     */
    public void releaseChunkFeatures(float[] chunkFeatures) {
        if (chunkFeatures != null) {
            VoiceObjectPool.releaseMfccFeatures(chunkFeatures);
        }
    }
} 