package com.emotion.voice.sdk;

import android.util.Log;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.List;
import java.util.ArrayList;

/**
 * 语音对象池 - 统一池化管理
 * 专为语音情感分析优化，提供高效的对象复用机制
 * 
 * 核心特性：
 * 1. MFCC特征数组(float[])池化
 * 2. 字符串列表(List<String>)池化
 * 3. 线程安全的无锁设计
 * 4. 智能性能监控
 * 5. 自动资源优化
 */
public class VoiceObjectPool {
    
    private static final String TAG = "VoiceObjectPool";
    
    // 池配置
    private static final int CORE_POOL_SIZE = 16;     // 核心池大小
    private static final int MAX_POOL_SIZE = 64;      // 最大池大小
    private static final int WARMUP_SIZE = 8;         // 预热大小
    private static final int MFCC_DIMENSIONS = 48 * 39; // MFCC特征维度：48时间帧 × 39特征 = 1872
    
    // MFCC特征数组池
    private static final ConcurrentLinkedQueue<float[]> mfccPool = new ConcurrentLinkedQueue<>();
    private static final AtomicInteger mfccPoolSize = new AtomicInteger(0);
    
    // 字符串列表池
    private static final ConcurrentLinkedQueue<List<String>> stringListPool = new ConcurrentLinkedQueue<>();
    private static final AtomicInteger stringListPoolSize = new AtomicInteger(0);
    
    // 音频缓冲区池
    private static final ConcurrentLinkedQueue<short[]> audioBufferPool = new ConcurrentLinkedQueue<>();
    private static final AtomicInteger audioBufferPoolSize = new AtomicInteger(0);
    private static final int AUDIO_BUFFER_SIZE = 8000; // 500ms音频缓冲区大小 (16000Hz * 0.5s)
    
    // 性能统计
    private static final AtomicLong mfccRequests = new AtomicLong(0);
    private static final AtomicLong mfccHits = new AtomicLong(0);
    private static final AtomicLong mfccDynamicCreations = new AtomicLong(0);
    
    private static final AtomicLong stringListRequests = new AtomicLong(0);
    private static final AtomicLong stringListHits = new AtomicLong(0);
    private static final AtomicLong stringListDynamicCreations = new AtomicLong(0);
    
    private static final AtomicLong audioBufferRequests = new AtomicLong(0);
    private static final AtomicLong audioBufferHits = new AtomicLong(0);
    private static final AtomicLong audioBufferDynamicCreations = new AtomicLong(0);
    
    // 初始化标志
    private static volatile boolean initialized = false;
    
    /**
     * 静态初始化 - 预热池
     */
    static {
        initialize();
    }
    
    /**
     * 初始化对象池
     */
    private static void initialize() {
        if (initialized) return;
        
        try {
            // 预分配MFCC特征数组
            for (int i = 0; i < WARMUP_SIZE; i++) {
                float[] mfcc = new float[MFCC_DIMENSIONS];
                mfccPool.offer(mfcc);
                mfccPoolSize.incrementAndGet();
            }
            
            // 预分配字符串列表
            for (int i = 0; i < WARMUP_SIZE; i++) {
                List<String> list = new ArrayList<>();
                stringListPool.offer(list);
                stringListPoolSize.incrementAndGet();
            }
            
            // 预分配音频缓冲区
            for (int i = 0; i < WARMUP_SIZE; i++) {
                short[] buffer = new short[AUDIO_BUFFER_SIZE];
                audioBufferPool.offer(buffer);
                audioBufferPoolSize.incrementAndGet();
            }
            
            initialized = true;
            Log.i(TAG, "语音对象池初始化完成");
            Log.i(TAG, String.format("预分配: MFCC=%d个, StringList=%d个", WARMUP_SIZE, WARMUP_SIZE));
            
        } catch (Exception e) {
            Log.e(TAG, "对象池初始化失败", e);
        }
    }
    
    // ==================== MFCC特征数组管理 ====================
    
    /**
     * 获取MFCC特征数组
     */
    public static float[] acquireMfccFeatures() {
        mfccRequests.incrementAndGet();
        
        // 快速路径：从池中获取
        float[] features = mfccPool.poll();
        if (features != null) {
            mfccHits.incrementAndGet();
            mfccPoolSize.decrementAndGet();
            
            // 清零数组
            for (int i = 0; i < features.length; i++) {
                features[i] = 0.0f;
            }
            return features;
        }
        
        // 池为空，动态创建
        mfccDynamicCreations.incrementAndGet();
        return new float[MFCC_DIMENSIONS];
    }
    
    /**
     * 归还MFCC特征数组
     */
    public static void releaseMfccFeatures(float[] features) {
        if (features == null || features.length != MFCC_DIMENSIONS) {
            return;
        }
        
        // 检查池大小，避免过度积累
        if (mfccPoolSize.get() < MAX_POOL_SIZE) {
            mfccPool.offer(features);
            mfccPoolSize.incrementAndGet();
        }
        // 池满时让对象被GC回收
    }
    
    // ==================== 字符串列表管理 ====================
    
    /**
     * 获取字符串列表
     */
    public static List<String> acquireStringList() {
        stringListRequests.incrementAndGet();
        
        // 快速路径：从池中获取
        List<String> list = stringListPool.poll();
        if (list != null) {
            stringListHits.incrementAndGet();
            stringListPoolSize.decrementAndGet();
            list.clear(); // 确保清空
            return list;
        }
        
        // 池为空，动态创建
        stringListDynamicCreations.incrementAndGet();
        return new ArrayList<>();
    }
    
    /**
     * 归还字符串列表
     */
    public static void releaseStringList(List<String> list) {
        if (list == null) return;
        
        // 检查池大小，避免过度积累
        if (stringListPoolSize.get() < MAX_POOL_SIZE) {
            list.clear(); // 清空内容
            stringListPool.offer(list);
            stringListPoolSize.incrementAndGet();
        }
        // 池满时让对象被GC回收
    }
    
    // ==================== 音频缓冲区管理 ====================
    
    /**
     * 获取音频缓冲区
     */
    public static short[] acquireAudioBuffer() {
        audioBufferRequests.incrementAndGet();
        
        // 快速路径：从池中获取
        short[] buffer = audioBufferPool.poll();
        if (buffer != null) {
            audioBufferHits.incrementAndGet();
            audioBufferPoolSize.decrementAndGet();
            
            // 清零数组
            for (int i = 0; i < buffer.length; i++) {
                buffer[i] = 0;
            }
            return buffer;
        }
        
        // 池为空，动态创建
        audioBufferDynamicCreations.incrementAndGet();
        return new short[AUDIO_BUFFER_SIZE];
    }
    
    /**
     * 归还音频缓冲区
     */
    public static void releaseAudioBuffer(short[] buffer) {
        if (buffer == null) return;
        
        // 检查池大小，避免过度积累
        if (audioBufferPoolSize.get() < MAX_POOL_SIZE) {
            audioBufferPool.offer(buffer);
            audioBufferPoolSize.incrementAndGet();
        }
        // 池满时让对象被GC回收
    }
    
    // ==================== 高级功能 ====================
    
    /**
     * 预热对象池
     */
    public static void warmup() {
        Log.i(TAG, "开始预热语音对象池");
        
        // 预热MFCC池到核心大小
        int mfccTargetSize = CORE_POOL_SIZE - mfccPoolSize.get();
        for (int i = 0; i < mfccTargetSize; i++) {
            float[] mfcc = new float[MFCC_DIMENSIONS];
            mfccPool.offer(mfcc);
            mfccPoolSize.incrementAndGet();
        }
        
        // 预热字符串列表池
        int listTargetSize = CORE_POOL_SIZE - stringListPoolSize.get();
        for (int i = 0; i < listTargetSize; i++) {
            List<String> list = new ArrayList<>();
            stringListPool.offer(list);
            stringListPoolSize.incrementAndGet();
        }
        
        // 预热音频缓冲区池
        int audioTargetSize = CORE_POOL_SIZE - audioBufferPoolSize.get();
        for (int i = 0; i < audioTargetSize; i++) {
            short[] buffer = new short[AUDIO_BUFFER_SIZE];
            audioBufferPool.offer(buffer);
            audioBufferPoolSize.incrementAndGet();
        }
        
        Log.i(TAG, "语音对象池预热完成");
        Log.i(TAG, getDetailedStats());
    }
    
    /**
     * 优化对象池
     */
    public static void optimize() {
        // 保持核心池大小，清理多余对象
        while (mfccPoolSize.get() > CORE_POOL_SIZE) {
            float[] mfcc = mfccPool.poll();
            if (mfcc != null) {
                mfccPoolSize.decrementAndGet();
            } else {
                break;
            }
        }
        
        // 优化字符串列表池
        while (stringListPoolSize.get() > CORE_POOL_SIZE) {
            List<String> list = stringListPool.poll();
            if (list != null) {
                stringListPoolSize.decrementAndGet();
            } else {
                break;
            }
        }
        
        // 优化音频缓冲区池
        while (audioBufferPoolSize.get() > CORE_POOL_SIZE) {
            short[] buffer = audioBufferPool.poll();
            if (buffer != null) {
                audioBufferPoolSize.decrementAndGet();
            } else {
                break;
            }
        }
        
        Log.i(TAG, "语音对象池优化完成");
    }
    
    /**
     * 获取性能统计
     */
    public static String getStats() {
        long mfccTotal = mfccRequests.get();
        long mfccHitTotal = mfccHits.get();
        int mfccHitRate = mfccTotal > 0 ? (int) (mfccHitTotal * 100 / mfccTotal) : 0;
        
        long listTotal = stringListRequests.get();
        long listHitTotal = stringListHits.get();
        int listHitRate = listTotal > 0 ? (int) (listHitTotal * 100 / listTotal) : 0;
        
        return String.format("Pool: %d/%d MFCC(%d%%), %d/%d List(%d%%)", 
            mfccPoolSize.get(), MAX_POOL_SIZE, mfccHitRate,
            stringListPoolSize.get(), MAX_POOL_SIZE, listHitRate);
    }
    
    /**
     * 获取详细统计信息
     */
    public static String getDetailedStats() {
        long mfccTotal = mfccRequests.get();
        long mfccHitTotal = mfccHits.get();
        long mfccDynamicTotal = mfccDynamicCreations.get();
        int mfccHitRate = mfccTotal > 0 ? (int) (mfccHitTotal * 100 / mfccTotal) : 0;
        
        long listTotal = stringListRequests.get();
        long listHitTotal = stringListHits.get();
        long listDynamicTotal = stringListDynamicCreations.get();
        int listHitRate = listTotal > 0 ? (int) (listHitTotal * 100 / listTotal) : 0;
        
        return String.format(
            "=== 语音对象池统计 ===\n" +
            "MFCC池: %d/%d个对象\n" +
            "StringList池: %d/%d个对象\n" +
            "MFCC请求: %d次 (命中:%d次, 动态:%d次, 命中率:%d%%)\n" +
            "StringList请求: %d次 (命中:%d次, 动态:%d次, 命中率:%d%%)\n" +
            "===================",
            mfccPoolSize.get(), MAX_POOL_SIZE,
            stringListPoolSize.get(), MAX_POOL_SIZE,
            mfccTotal, mfccHitTotal, mfccDynamicTotal, mfccHitRate,
            listTotal, listHitTotal, listDynamicTotal, listHitRate
        );
    }
    
    /**
     * 检查池健康状态
     */
    public static boolean isHealthy() {
        // 检查MFCC池
        long mfccTotal = mfccRequests.get();
        if (mfccTotal >= 100) {
            long mfccHitTotal = mfccHits.get();
            int mfccHitRate = (int) (mfccHitTotal * 100 / mfccTotal);
            if (mfccHitRate <= 70) return false;
        }
        
        // 检查字符串列表池
        long listTotal = stringListRequests.get();
        if (listTotal >= 100) {
            long listHitTotal = stringListHits.get();
            int listHitRate = (int) (listHitTotal * 100 / listTotal);
            if (listHitRate <= 70) return false;
        }
        
        // 检查池大小
        boolean goodPoolSize = mfccPoolSize.get() >= 0 && mfccPoolSize.get() <= MAX_POOL_SIZE &&
                               stringListPoolSize.get() >= 0 && stringListPoolSize.get() <= MAX_POOL_SIZE;
        
        return goodPoolSize;
    }
    
    /**
     * 清理资源
     */
    public static void cleanup() {
        Log.i(TAG, "开始清理语音对象池资源");
        
        // 清理MFCC池
        mfccPool.clear();
        mfccPoolSize.set(0);
        
        // 清理字符串列表池
        stringListPool.clear();
        stringListPoolSize.set(0);
        
        // 清理音频缓冲区池
        audioBufferPool.clear();
        audioBufferPoolSize.set(0);
        
        // 重置统计
        mfccRequests.set(0);
        mfccHits.set(0);
        mfccDynamicCreations.set(0);
        
        stringListRequests.set(0);
        stringListHits.set(0);
        stringListDynamicCreations.set(0);
        
        audioBufferRequests.set(0);
        audioBufferHits.set(0);
        audioBufferDynamicCreations.set(0);
        
        Log.i(TAG, "语音对象池资源清理完成");
    }
} 