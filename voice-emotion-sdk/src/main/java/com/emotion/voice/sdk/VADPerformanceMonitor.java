package com.emotion.voice.sdk;

import android.util.Log;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 增强的Voice Activity Detection性能监控器
 */
public class VADPerformanceMonitor {
    
    private static final String TAG = "VADPerformanceMonitor";
    
    // 单例模式
    private static volatile VADPerformanceMonitor instance;
    
    // 性能阈值常量
    private static final long MAX_PROCESSING_TIME_MS = 50;
    private static final double MIN_DETECTION_RATE = 0.05;
    private static final double OPTIMAL_DETECTION_RATE = 0.15;
    private static final double MAX_DETECTION_RATE = 0.80;
    private static final long MEMORY_WARNING_THRESHOLD = 10 * 1024 * 1024; // 10MB
    private static final int PERFORMANCE_REPORT_INTERVAL = 1000; // 每1000帧报告一次
    
    // Performance counters
    private final AtomicInteger totalFrames = new AtomicInteger(0);
    private final AtomicInteger voiceFrames = new AtomicInteger(0);
    private final AtomicInteger silenceFrames = new AtomicInteger(0);
    private final AtomicInteger emotionDetections = new AtomicInteger(0);
    private final AtomicInteger errors = new AtomicInteger(0);
    
    // Timing metrics
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    private final AtomicLong minProcessingTime = new AtomicLong(Long.MAX_VALUE);
    private final AtomicLong maxProcessingTime = new AtomicLong(0);
    private final AtomicLong startTime = new AtomicLong(0);
    
    // Quality metrics
    private final AtomicInteger falsePositives = new AtomicInteger(0);
    private final AtomicInteger falseNegatives = new AtomicInteger(0);
    private final AtomicInteger truePositives = new AtomicInteger(0);
    private final AtomicInteger trueNegatives = new AtomicInteger(0);
    
    // 增强功能
    private final AtomicReference<PerformanceGrade> currentGrade = new AtomicReference<>(PerformanceGrade.UNKNOWN);
    private final AtomicInteger consecutiveSlowFrames = new AtomicInteger(0);
    private final AtomicInteger consecutiveErrorFrames = new AtomicInteger(0);
    private final AtomicLong lastOptimizationSuggestionTime = new AtomicLong(0);
    private final AtomicLong totalMemoryUsage = new AtomicLong(0);
    private final AtomicInteger memoryMeasurements = new AtomicInteger(0);
    
    // 性能分析窗口
    private final AtomicInteger recentFrames = new AtomicInteger(0);
    private final AtomicInteger recentVoiceFrames = new AtomicInteger(0);
    private final AtomicLong recentProcessingTime = new AtomicLong(0);
    private final AtomicLong windowStartTime = new AtomicLong(0);
    private static final long ANALYSIS_WINDOW_MS = 10000; // 10秒分析窗口
    
    // Configuration
    private boolean enableDetailedLogging = false;
    private boolean enableAutoOptimization = true;
    private int reportingInterval = PERFORMANCE_REPORT_INTERVAL;
    
    /**
     * 性能评级枚举
     */
    public enum PerformanceGrade {
        UNKNOWN("未知", "刚启动，数据不足"),
        EXCELLENT("A+ 优秀", "所有指标都在最优范围"),
        GOOD("A 良好", "大部分指标良好"),
        AVERAGE("B 一般", "指标一般，有改进空间"),
        POOR("C 需要优化", "多个指标不佳"),
        CRITICAL("D 性能不佳", "性能严重不足");
        
        private final String displayName;
        private final String description;
        
        PerformanceGrade(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    /**
     * 私有构造函数
     */
    private VADPerformanceMonitor() {
        reset();
    }
    
    /**
     * 获取单例实例
     */
    public static VADPerformanceMonitor getInstance() {
        if (instance == null) {
            synchronized (VADPerformanceMonitor.class) {
                if (instance == null) {
                    instance = new VADPerformanceMonitor();
                }
            }
        }
        return instance;
    }
    
    /**
     * Start monitoring
     */
    public void startMonitoring() {
        startTime.set(System.currentTimeMillis());
        windowStartTime.set(System.currentTimeMillis());
        resetRecentMetrics();
    }
    
    /**
     * Stop monitoring
     */
    public void stopMonitoring() {
        long duration = System.currentTimeMillis() - startTime.get();
        logFinalReport();
    }
    
    /**
     * Record frame processing with enhanced analysis
     */
    public void recordFrameProcessing(long processingTime, boolean hasVoice) {
        totalFrames.incrementAndGet();
        recentFrames.incrementAndGet();
        
        if (hasVoice) {
            voiceFrames.incrementAndGet();
            recentVoiceFrames.incrementAndGet();
        } else {
            silenceFrames.incrementAndGet();
        }
        
        recordProcessingTime(processingTime);
        
        // 性能分析
        analyzePerformance(processingTime);
        
        // 检查是否需要重置分析窗口
        checkAnalysisWindow();
        
        // 定期报告
        if (totalFrames.get() % reportingInterval == 0) {
            generatePerformanceReport();
        }
    }
    
    /**
     * Record emotion detection
     */
    public void recordEmotionDetection(String emotion, long processingTime) {
        emotionDetections.incrementAndGet();
        recordProcessingTime(processingTime);
        
        // 记录内存使用情况
        recordMemoryUsage();
    }
    
    /**
     * Record processing time
     */
    private void recordProcessingTime(long processingTime) {
        totalProcessingTime.addAndGet(processingTime);
        recentProcessingTime.addAndGet(processingTime);
        
        // Update min/max
        long currentMin = minProcessingTime.get();
        while (processingTime < currentMin) {
            if (minProcessingTime.compareAndSet(currentMin, processingTime)) {
                break;
            }
            currentMin = minProcessingTime.get();
        }
        
        long currentMax = maxProcessingTime.get();
        while (processingTime > currentMax) {
            if (maxProcessingTime.compareAndSet(currentMax, processingTime)) {
                break;
            }
            currentMax = maxProcessingTime.get();
        }
    }
    
    /**
     * 性能分析
     */
    private void analyzePerformance(long processingTime) {
        // 检查处理时间
        if (processingTime > MAX_PROCESSING_TIME_MS) {
            int slowFrames = consecutiveSlowFrames.incrementAndGet();
            if (slowFrames > 5 && enableAutoOptimization) {
                suggestOptimization("处理时间过长", "建议启用缓存机制或降低检测精度");
            }
        } else {
            consecutiveSlowFrames.set(0);
        }
        
        // 更新性能评级
        updatePerformanceGrade();
    }
    
    /**
     * 更新性能评级
     */
    private void updatePerformanceGrade() {
        double avgTime = getAverageProcessingTime();
        double accuracy = getAccuracy();
        double detectionRate = getDetectionRate();
        
        PerformanceGrade grade;
        
        if (avgTime <= MAX_PROCESSING_TIME_MS * 0.5 && accuracy >= 0.95 && 
            detectionRate >= MIN_DETECTION_RATE && detectionRate <= OPTIMAL_DETECTION_RATE) {
            grade = PerformanceGrade.EXCELLENT;
        } else if (avgTime <= MAX_PROCESSING_TIME_MS * 0.8 && accuracy >= 0.85 && 
                   detectionRate >= MIN_DETECTION_RATE && detectionRate <= MAX_DETECTION_RATE) {
            grade = PerformanceGrade.GOOD;
        } else if (avgTime <= MAX_PROCESSING_TIME_MS && accuracy >= 0.70 && 
                   detectionRate >= MIN_DETECTION_RATE) {
            grade = PerformanceGrade.AVERAGE;
        } else if (avgTime <= MAX_PROCESSING_TIME_MS * 1.5 && accuracy >= 0.50) {
            grade = PerformanceGrade.POOR;
        } else {
            grade = PerformanceGrade.CRITICAL;
        }
        
        currentGrade.set(grade);
    }
    
    /**
     * 优化建议
     */
    private void suggestOptimization(String issue, String suggestion) {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastOptimizationSuggestionTime.get() > 30000) { // 30秒间隔
            lastOptimizationSuggestionTime.set(currentTime);
            Log.i(TAG, String.format("性能优化建议 - %s: %s", issue, suggestion));
        }
    }
    
    /**
     * 记录内存使用情况
     */
    private void recordMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        totalMemoryUsage.addAndGet(usedMemory);
        memoryMeasurements.incrementAndGet();
        
        if (usedMemory > MEMORY_WARNING_THRESHOLD) {
            suggestOptimization("内存使用过高", "建议优化缓存策略或降低处理频率");
        }
    }
    
    /**
     * 检查分析窗口
     */
    private void checkAnalysisWindow() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - windowStartTime.get() > ANALYSIS_WINDOW_MS) {
            // 重置分析窗口
            resetRecentMetrics();
            windowStartTime.set(currentTime);
        }
    }
    
    /**
     * 重置最近指标
     */
    private void resetRecentMetrics() {
        recentFrames.set(0);
        recentVoiceFrames.set(0);
        recentProcessingTime.set(0);
    }
    
    /**
     * Record error
     */
    public void recordError(String errorType) {
        errors.incrementAndGet();
        int errorFrames = consecutiveErrorFrames.incrementAndGet();
        
        if (errorFrames > 3 && enableAutoOptimization) {
            suggestOptimization("连续错误", "建议检查音频设备或降低处理复杂度");
        }
        
        // 只在错误率过高时输出警告
        if (errors.get() > totalFrames.get() * 0.1) {
            Log.w(TAG, "❌ 错误率过高: " + errorType);
        }
    }
    
    /**
     * Record detection quality metrics
     */
    public void recordDetectionQuality(boolean actualVoice, boolean detectedVoice) {
        if (actualVoice && detectedVoice) {
            truePositives.incrementAndGet();
        } else if (!actualVoice && !detectedVoice) {
            trueNegatives.incrementAndGet();
        } else if (!actualVoice && detectedVoice) {
            falsePositives.incrementAndGet();
        } else if (actualVoice && !detectedVoice) {
            falseNegatives.incrementAndGet();
        }
        
        // 重置连续错误计数
        consecutiveErrorFrames.set(0);
    }
    
    /**
     * 生成性能报告
     */
    private void generatePerformanceReport() {
        PerformanceMetrics metrics = getMetrics();
        PerformanceGrade grade = currentGrade.get();
        
        // 只在性能不佳时输出详细报告
        if (grade == PerformanceGrade.POOR || grade == PerformanceGrade.CRITICAL) {
            Log.w(TAG, String.format("性能报告 [%s] - 平均时间:%.1fms, 检测率:%.1f%%, 准确率:%.1f%%",
                grade.getDisplayName(), metrics.avgProcessingTime, 
                metrics.detectionRate * 100, metrics.accuracy * 100));
        }
        
        // 自动优化建议
        if (enableAutoOptimization) {
            generateOptimizationSuggestions(metrics);
        }
    }
    
    /**
     * 生成优化建议
     */
    private void generateOptimizationSuggestions(PerformanceMetrics metrics) {
        if (metrics.avgProcessingTime > MAX_PROCESSING_TIME_MS) {
            suggestOptimization("处理时间过长", "建议启用缓存机制");
        }
        
        if (metrics.detectionRate < MIN_DETECTION_RATE) {
            suggestOptimization("检测率过低", "建议降低检测阈值");
        }
        
        if (metrics.detectionRate > MAX_DETECTION_RATE) {
            suggestOptimization("检测率过高", "可能存在噪声干扰，建议提高阈值");
        }
        
        if (metrics.accuracy < 0.7) {
            suggestOptimization("准确率偏低", "建议调整算法参数或增加训练数据");
        }
        
        if (getAverageMemoryUsage() > MEMORY_WARNING_THRESHOLD) {
            suggestOptimization("内存使用过高", "建议优化内存管理或降低缓存大小");
        }
    }
    
    /**
     * Get performance metrics
     */
    public PerformanceMetrics getMetrics() {
        return new PerformanceMetrics(
            totalFrames.get(),
            voiceFrames.get(),
            silenceFrames.get(),
            emotionDetections.get(),
            errors.get(),
            getAverageProcessingTime(),
            minProcessingTime.get() == Long.MAX_VALUE ? 0 : minProcessingTime.get(),
            maxProcessingTime.get(),
            getAccuracy(),
            getPrecision(),
            getRecall(),
            getF1Score(),
            getDetectionRate(),
            getThroughput(),
            getAverageMemoryUsage(),
            currentGrade.get()
        );
    }
    
    /**
     * Calculate average processing time
     */
    public double getAverageProcessingTime() {
        int frames = totalFrames.get();
        return frames > 0 ? (double) totalProcessingTime.get() / frames : 0;
    }
    
    /**
     * Calculate detection rate
     */
    public double getDetectionRate() {
        int total = totalFrames.get();
        return total > 0 ? (double) voiceFrames.get() / total : 0;
    }
    
    /**
     * Calculate throughput (frames per second)
     */
    public double getThroughput() {
        long duration = System.currentTimeMillis() - startTime.get();
        return duration > 0 ? (double) totalFrames.get() / (duration / 1000.0) : 0;
    }
    
    /**
     * Calculate average memory usage
     */
    public long getAverageMemoryUsage() {
        int measurements = memoryMeasurements.get();
        return measurements > 0 ? totalMemoryUsage.get() / measurements : 0;
    }
    
    /**
     * Calculate accuracy
     */
    public double getAccuracy() {
        int total = truePositives.get() + trueNegatives.get() + falsePositives.get() + falseNegatives.get();
        return total > 0 ? (double) (truePositives.get() + trueNegatives.get()) / total : 0;
    }
    
    /**
     * Calculate precision
     */
    public double getPrecision() {
        int positives = truePositives.get() + falsePositives.get();
        return positives > 0 ? (double) truePositives.get() / positives : 0;
    }
    
    /**
     * Calculate recall
     */
    public double getRecall() {
        int actualPositives = truePositives.get() + falseNegatives.get();
        return actualPositives > 0 ? (double) truePositives.get() / actualPositives : 0;
    }
    
    /**
     * Calculate F1 score
     */
    public double getF1Score() {
        double precision = getPrecision();
        double recall = getRecall();
        return (precision + recall) > 0 ? 2 * precision * recall / (precision + recall) : 0;
    }
    
    /**
     * Get performance summary (借鉴demo项目)
     */
    public String getPerformanceSummary() {
        PerformanceMetrics metrics = getMetrics();
        return String.format(java.util.Locale.US,
            "VAD性能 [%s] - 平均时间:%.1fms, 检测率:%.1f%%, 吞吐量:%.1f/s",
            metrics.grade.getDisplayName(),
            metrics.avgProcessingTime,
            metrics.detectionRate * 100,
            metrics.throughput);
    }
    
    /**
     * Get detailed performance report
     */
    public String getDetailedPerformanceReport() {
        PerformanceMetrics metrics = getMetrics();
        return String.format(java.util.Locale.US,
            "=== VAD性能详细报告 ===\n" +
            "性能评级: %s\n" +
            "总帧数: %d (语音: %d, 静音: %d)\n" +
            "处理时间: 平均%.1fms, 最小%dms, 最大%dms\n" +
            "检测率: %.1f%% (目标: %.1f%%-%.1f%%)\n" +
            "准确率: %.2f%%, 精确率: %.2f%%, 召回率: %.2f%%\n" +
            "F1得分: %.2f\n" +
            "吞吐量: %.1f帧/秒\n" +
            "内存使用: 平均%.1fMB\n" +
            "错误数: %d\n" +
            "情感检测: %d次",
            metrics.grade.getDisplayName(),
            metrics.totalFrames, metrics.voiceFrames, metrics.silenceFrames,
            metrics.avgProcessingTime, metrics.minProcessingTime, metrics.maxProcessingTime,
            metrics.detectionRate * 100, MIN_DETECTION_RATE * 100, OPTIMAL_DETECTION_RATE * 100,
            metrics.accuracy * 100, metrics.precision * 100, metrics.recall * 100,
            metrics.f1Score,
            metrics.throughput,
            metrics.averageMemoryUsage / (1024.0 * 1024.0),
            metrics.errors,
            metrics.emotionDetections);
    }
    
    /**
     * Log final report
     */
    private void logFinalReport() {
        PerformanceMetrics metrics = getMetrics();
        Log.i(TAG, "=== 最终性能报告 ===");
        Log.i(TAG, String.format("总帧数: %d", metrics.totalFrames));
        Log.i(TAG, String.format("语音帧: %d (%.1f%%)", 
            metrics.voiceFrames, 
            metrics.totalFrames > 0 ? 100.0 * metrics.voiceFrames / metrics.totalFrames : 0));
        Log.i(TAG, String.format("处理时间: 平均%.1fms, 最小%dms, 最大%dms", 
            metrics.avgProcessingTime, metrics.minProcessingTime, metrics.maxProcessingTime));
        Log.i(TAG, String.format("性能评级: %s", metrics.grade.getDisplayName()));
        Log.i(TAG, String.format("准确率: %.2f%%", metrics.accuracy * 100));
        Log.i(TAG, String.format("吞吐量: %.1f帧/秒", metrics.throughput));
        Log.i(TAG, String.format("错误数: %d", metrics.errors));
    }
    
    /**
     * Reset all metrics
     */
    public void reset() {
        totalFrames.set(0);
        voiceFrames.set(0);
        silenceFrames.set(0);
        emotionDetections.set(0);
        errors.set(0);
        totalProcessingTime.set(0);
        minProcessingTime.set(Long.MAX_VALUE);
        maxProcessingTime.set(0);
        falsePositives.set(0);
        falseNegatives.set(0);
        truePositives.set(0);
        trueNegatives.set(0);
        startTime.set(0);
        
        // 重置增强功能
        currentGrade.set(PerformanceGrade.UNKNOWN);
        consecutiveSlowFrames.set(0);
        consecutiveErrorFrames.set(0);
        lastOptimizationSuggestionTime.set(0);
        totalMemoryUsage.set(0);
        memoryMeasurements.set(0);
        
        resetRecentMetrics();
    }
    
    /**
     * Configuration methods
     */
    public void setEnableDetailedLogging(boolean enable) {
        this.enableDetailedLogging = enable;
    }
    
    public void setEnableAutoOptimization(boolean enable) {
        this.enableAutoOptimization = enable;
    }
    
    public void setReportingInterval(int interval) {
        this.reportingInterval = interval;
    }
    
    public PerformanceGrade getCurrentGrade() {
        return currentGrade.get();
    }
    
    public boolean isHealthy() {
        PerformanceGrade grade = currentGrade.get();
        return grade == PerformanceGrade.EXCELLENT || grade == PerformanceGrade.GOOD;
    }
    
    /**
     * Enhanced performance metrics data class
     */
    public static class PerformanceMetrics {
        public final int totalFrames;
        public final int voiceFrames;
        public final int silenceFrames;
        public final int emotionDetections;
        public final int errors;
        public final double avgProcessingTime;
        public final long minProcessingTime;
        public final long maxProcessingTime;
        public final double accuracy;
        public final double precision;
        public final double recall;
        public final double f1Score;
        public final double detectionRate;
        public final double throughput;
        public final long averageMemoryUsage;
        public final PerformanceGrade grade;
        
        public PerformanceMetrics(int totalFrames, int voiceFrames, int silenceFrames,
                                int emotionDetections, int errors, double avgProcessingTime,
                                long minProcessingTime, long maxProcessingTime,
                                double accuracy, double precision, double recall, double f1Score,
                                double detectionRate, double throughput, long averageMemoryUsage,
                                PerformanceGrade grade) {
            this.totalFrames = totalFrames;
            this.voiceFrames = voiceFrames;
            this.silenceFrames = silenceFrames;
            this.emotionDetections = emotionDetections;
            this.errors = errors;
            this.avgProcessingTime = avgProcessingTime;
            this.minProcessingTime = minProcessingTime;
            this.maxProcessingTime = maxProcessingTime;
            this.accuracy = accuracy;
            this.precision = precision;
            this.recall = recall;
            this.f1Score = f1Score;
            this.detectionRate = detectionRate;
            this.throughput = throughput;
            this.averageMemoryUsage = averageMemoryUsage;
            this.grade = grade;
        }
    }
} 