package com.emotion.voice.sdk;

import android.content.Context;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.util.Log;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 音频处理器 - 定时轮询版
 * 职责：持续填充滚动缓冲区，提供音频数据给定时器轮询
 */
public class AudioProcessor {
    private static final String TAG = "AudioProcessor";

    private static final int SAMPLE_RATE = 16000;
    private static final int CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO;
    private static final int AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT;
    private static final int READ_FRAME_SIZE = 160; // 10ms @ 16kHz

    /**
     * 音频设备诊断测试方法
     * 在实际初始化前检查音频设备状态
     */
    public static void diagnoseAudioDevice(Context context) {
        Log.i(TAG, "=== 开始音频设备诊断 ===");
        
        try {
            // 1. 检查基本权限
            if (context.checkSelfPermission(android.Manifest.permission.RECORD_AUDIO) 
                != android.content.pm.PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "❌ RECORD_AUDIO权限未授予");
                return;
            }
            Log.i(TAG, "✅ RECORD_AUDIO权限已授予");
            
            // 2. 检查硬件支持
            android.content.pm.PackageManager pm = context.getPackageManager();
            boolean hasMicrophone = pm.hasSystemFeature(android.content.pm.PackageManager.FEATURE_MICROPHONE);
            Log.i(TAG, "硬件麦克风支持: " + (hasMicrophone ? "✅" : "❌"));
            
            // 3. 检查音频管理器
            android.media.AudioManager audioManager = (android.media.AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            if (audioManager == null) {
                Log.e(TAG, "❌ AudioManager不可用");
                return;
            }
            Log.i(TAG, "✅ AudioManager可用");
            Log.i(TAG, "  - 音频模式: " + audioManager.getMode());
            Log.i(TAG, "  - 是否连接耳机: " + audioManager.isWiredHeadsetOn());
            Log.i(TAG, "  - 是否连接蓝牙: " + audioManager.isBluetoothA2dpOn());
            
            // 4. 测试不同的音频参数组合
            Log.i(TAG, "=== 测试音频参数组合 ===");
            testAudioParameters(16000, AudioFormat.CHANNEL_IN_MONO, AudioFormat.ENCODING_PCM_16BIT, "16kHz单声道16位");
            testAudioParameters(44100, AudioFormat.CHANNEL_IN_MONO, AudioFormat.ENCODING_PCM_16BIT, "44.1kHz单声道16位");
            testAudioParameters(8000, AudioFormat.CHANNEL_IN_MONO, AudioFormat.ENCODING_PCM_16BIT, "8kHz单声道16位");
            testAudioParameters(16000, AudioFormat.CHANNEL_IN_STEREO, AudioFormat.ENCODING_PCM_16BIT, "16kHz双声道16位");
            
            // 5. 测试不同的音频源
            Log.i(TAG, "=== 测试不同音频源 ===");
            testAudioSource(MediaRecorder.AudioSource.MIC, "MIC");
            testAudioSource(MediaRecorder.AudioSource.DEFAULT, "DEFAULT");
            testAudioSource(MediaRecorder.AudioSource.VOICE_RECOGNITION, "VOICE_RECOGNITION");
            testAudioSource(MediaRecorder.AudioSource.VOICE_COMMUNICATION, "VOICE_COMMUNICATION");
            
        } catch (Exception e) {
            Log.e(TAG, "音频设备诊断异常", e);
        }
        
        Log.i(TAG, "=== 音频设备诊断完成 ===");
    }
    
    private static void testAudioParameters(int sampleRate, int channelConfig, int audioFormat, String description) {
        try {
            int minBufferSize = AudioRecord.getMinBufferSize(sampleRate, channelConfig, audioFormat);
            Log.i(TAG, description + " - 最小缓冲区: " + minBufferSize);
            
            if (minBufferSize == AudioRecord.ERROR_BAD_VALUE) {
                Log.e(TAG, "  ❌ 参数组合不支持");
                return;
            }
            if (minBufferSize == AudioRecord.ERROR) {
                Log.e(TAG, "  ❌ 参数查询失败");
                return;
            }
            
            // 尝试创建AudioRecord
            AudioRecord testRecord = new AudioRecord(MediaRecorder.AudioSource.MIC, sampleRate, channelConfig, audioFormat, minBufferSize * 2);
            int state = testRecord.getState();
            Log.i(TAG, "  状态: " + state + " " + (state == AudioRecord.STATE_INITIALIZED ? "✅" : "❌"));
            
            if (state == AudioRecord.STATE_INITIALIZED) {
                Log.i(TAG, "  ✅ " + description + " 支持");
            } else {
                Log.e(TAG, "  ❌ " + description + " 不支持，状态码: " + state);
            }
            
            testRecord.release();
        } catch (Exception e) {
            Log.e(TAG, "  ❌ " + description + " 测试异常: " + e.getMessage());
        }
    }
    
    private static void testAudioSource(int audioSource, String sourceName) {
        try {
            int minBufferSize = AudioRecord.getMinBufferSize(16000, AudioFormat.CHANNEL_IN_MONO, AudioFormat.ENCODING_PCM_16BIT);
            if (minBufferSize > 0) {
                AudioRecord testRecord = new AudioRecord(audioSource, 16000, AudioFormat.CHANNEL_IN_MONO, AudioFormat.ENCODING_PCM_16BIT, minBufferSize * 2);
                int state = testRecord.getState();
                Log.i(TAG, "音频源 " + sourceName + " - 状态: " + state + " " + (state == AudioRecord.STATE_INITIALIZED ? "✅" : "❌"));
                testRecord.release();
            }
        } catch (Exception e) {
            Log.e(TAG, "音频源 " + sourceName + " 测试异常: " + e.getMessage());
        }
    }

    // 滚动缓冲区配置和成员变量
    private static final int ROLLING_BUFFER_DURATION_MS = 1500;
    private final short[] rollingAudioBuffer = new short[SAMPLE_RATE * ROLLING_BUFFER_DURATION_MS / 1000];
    private final AtomicInteger bufferWritePosition = new AtomicInteger(0);
    private final Object bufferLock = new Object();

    private AudioRecord audioRecord;
    private final AtomicBoolean isRecording = new AtomicBoolean(false);
    private Thread recordingThread;
    private Context context; // 保存context供后续初始化使用

    public AudioProcessor(Context context) {
        this.context = context.getApplicationContext();
        // 不在构造函数中初始化AudioRecord，延迟到explicit初始化
    }
    
    /**
     * 显式初始化AudioRecord
     * 应该在确保权限和环境都准备好后调用
     */
    public boolean initializeAudioRecord() {
        if (isInitialized()) {
            return true; // 已经初始化过了
        }
        
        try {
            int minBufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT);
            Log.i(TAG, "AudioRecord最小缓冲区大小: " + minBufferSize);
            
            if (minBufferSize == AudioRecord.ERROR_BAD_VALUE) {
                Log.e(TAG, "AudioRecord.getMinBufferSize() 返回 ERROR_BAD_VALUE");
                return false;
            }
            if (minBufferSize == AudioRecord.ERROR) {
                Log.e(TAG, "AudioRecord.getMinBufferSize() 返回 ERROR");
                return false;
            }
            
            audioRecord = new AudioRecord(MediaRecorder.AudioSource.MIC, SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT, minBufferSize * 2);
            
            int state = audioRecord.getState();
            Log.i(TAG, "AudioRecord状态: " + state + " (STATE_INITIALIZED=" + AudioRecord.STATE_INITIALIZED + ")");
            
            if (state != AudioRecord.STATE_INITIALIZED) {
                Log.e(TAG, "AudioRecord初始化失败，状态: " + state);
                if (audioRecord != null) {
                    audioRecord.release();
                    audioRecord = null;
                }
                return false;
            } else {
                Log.i(TAG, "AudioRecord初始化成功");
                return true;
            }
        } catch (Exception e) {
            Log.e(TAG, "AudioRecord初始化异常", e);
            if (audioRecord != null) {
                audioRecord.release();
                audioRecord = null;
            }
            return false;
        }
    }



    public boolean startAudioProcessing() {
        if (!isInitialized()) return false;
        if (isRecording.get()) return true;

        try {
            audioRecord.startRecording();
            isRecording.set(true);
            recordingThread = new Thread(this::processAudioLoop, "AudioProcessor");
            recordingThread.start();
            Log.i(TAG, "音频处理已启动");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "启动音频录制失败", e);
            return false;
        }
    }

    public void stopAudioProcessing() {
        if (!isRecording.get()) return;
        isRecording.set(false);
        if (recordingThread != null) {
            try {
                recordingThread.interrupt();
                recordingThread.join(100);
            } catch (InterruptedException e) { 
                Log.e(TAG, "录音线程中断异常", e); 
            }
        }
        if (audioRecord != null && audioRecord.getRecordingState() == AudioRecord.RECORDSTATE_RECORDING) {
            audioRecord.stop();
        }
        Log.i(TAG, "音频处理已停止");
    }

    private void processAudioLoop() {
        short[] audioFrame = new short[READ_FRAME_SIZE];
        while (isRecording.get() && !Thread.currentThread().isInterrupted()) {
            int samplesRead = audioRecord.read(audioFrame, 0, audioFrame.length);
            if (samplesRead > 0) {
                // 将读取到的音频数据写入滚动缓冲区
                synchronized (bufferLock) {
                    int pos = bufferWritePosition.get();
                    for (int i = 0; i < samplesRead; i++) {
                        rollingAudioBuffer[pos] = audioFrame[i];
                        pos = (pos + 1) % rollingAudioBuffer.length;
                    }
                    bufferWritePosition.set(pos);
                }
            }
        }
    }

    /**
     * 从滚动缓冲区获取指定时长的最近音频数据
     * @param durationMs 要获取的音频时长（毫秒）
     * @param destination 目标数组
     */
    public void getRecentAudioChunk(int durationMs, short[] destination) {
        int requiredSamples = SAMPLE_RATE * durationMs / 1000;
        
        if (destination.length < requiredSamples) {
            Log.w(TAG, "目标数组长度不足，需要" + requiredSamples + "，实际" + destination.length);
            requiredSamples = destination.length;
        }
        
        synchronized (bufferLock) {
            int currentPos = bufferWritePosition.get();
            int startPos = (currentPos - requiredSamples + rollingAudioBuffer.length) % rollingAudioBuffer.length;
            
            if (startPos + requiredSamples <= rollingAudioBuffer.length) {
                // 数据连续，一次性拷贝
                System.arraycopy(rollingAudioBuffer, startPos, destination, 0, requiredSamples);
            } else {
                // 数据跨越了缓冲区的结尾和开头，需要分两次拷贝
                int firstPartLength = rollingAudioBuffer.length - startPos;
                int secondPartLength = requiredSamples - firstPartLength;
                
                // 拷贝第一部分（从startPos到缓冲区结尾）
                System.arraycopy(rollingAudioBuffer, startPos, destination, 0, firstPartLength);
                
                // 拷贝第二部分（从缓冲区开头到所需长度）
                System.arraycopy(rollingAudioBuffer, 0, destination, firstPartLength, secondPartLength);
            }
        }
    }

    public boolean isInitialized() {
        return audioRecord != null && audioRecord.getState() == AudioRecord.STATE_INITIALIZED;
    }
    
    public void release() {
        stopAudioProcessing();
        if (audioRecord != null) {
            audioRecord.release();
        }
        Log.i(TAG, "AudioProcessor资源已释放");
    }
}