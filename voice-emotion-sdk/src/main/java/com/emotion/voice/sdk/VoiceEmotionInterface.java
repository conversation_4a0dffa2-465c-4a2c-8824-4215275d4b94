package com.emotion.voice.sdk;

import android.util.Log;

/**
 * 语音情感识别JNI接口
 * 提供与C++层的安全交互
 */
public class VoiceEmotionInterface {
    
    private static final String TAG = "VoiceEmotionInterface";
    private static boolean libraryLoaded = false;
    private boolean modelInitialized = false;
    
    static {
        try {
            // Log.i(TAG, "开始加载语音检测SDK库...");
            
            // 先加载依赖库
            // Log.d(TAG, "1. 加载依赖库 rknnrt...");
            System.loadLibrary("rknnrt");
            // Log.d(TAG, "✅ rknnrt 加载成功");
            
            // Log.d(TAG, "2. 加载依赖库 rga...");
            System.loadLibrary("rga");
            // Log.d(TAG, "✅ rga 加载成功");
            
            // 再加载主功能库
            // Log.d(TAG, "3. 加载主功能库 rknn_voice_detection_demo...");
            System.loadLibrary("rknn_voice_detection_demo");
            // Log.d(TAG, "✅ rknn_voice_detection_demo 加载成功");
            
            // 加载JNI包装库 - 使用新的库名称
            // Log.d(TAG, "4. 加载JNI包装库 voice_emotion_sdk...");
            System.loadLibrary("voice_emotion_sdk");
            // Log.d(TAG, "✅ voice_emotion_sdk 加载成功");
            
            // Log.i(TAG, "✅ 语音JNI库加载成功");
            libraryLoaded = true;
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "❌ 语音JNI库加载失败: " + e.getMessage());
            Log.e(TAG, "库加载详细错误", e);
            libraryLoaded = false;
        }
    }
    
    /**
     * 安全的模型初始化
     */
    public boolean initModel(String modelPath) {
        // Log.i(TAG, "开始初始化语音模型...");
        // Log.d(TAG, "模型路径: " + modelPath);
        
        if (!libraryLoaded) {
            Log.e(TAG, "❌ 库未加载，无法初始化模型");
            return false;
        }
        
        try {
            // Log.d(TAG, "调用JNI方法 initVoiceModel...");
            String result = initVoiceModel(modelPath);
            // Log.d(TAG, "JNI返回结果: " + result);
            
            boolean success = result != null && result.contains("Success");
            if (success) {
                modelInitialized = true;
                // Log.i(TAG, "✅ 语音模型初始化成功");
            } else {
                modelInitialized = false;
                Log.e(TAG, "❌ 语音模型初始化失败: " + result);
                // Log.i(TAG, "继续运行，但语音检测功能将不可用");
            }
            return success;
        } catch (UnsatisfiedLinkError e) {
            modelInitialized = false;
            Log.e(TAG, "❌ JNI方法未找到: " + e.getMessage());
            Log.e(TAG, "JNI详细错误", e);
            // Log.i(TAG, "可能是C++库未正确加载或方法名不匹配，继续运行但语音检测功能将不可用");
            return false;
        } catch (Exception e) {
            modelInitialized = false;
            Log.e(TAG, "❌ 语音模型初始化异常: " + e.getMessage());
            Log.e(TAG, "详细异常信息", e);
            // Log.i(TAG, "继续运行，但语音检测功能将不可用");
            return false;
        }
    }
    
    /**
     * 安全的语音检测
     */
    public String detectVoiceSafe(float[] audioFeatures) {
        if (!libraryLoaded) {
            return "neutral"; // 默认情感
        }
        
        try {
            return detectVoice(audioFeatures);
        } catch (Exception e) {
            Log.e(TAG, "❌ 语音检测异常: " + e.getMessage());
            return "neutral";
        }
    }
    
    /**
     * 安全的资源释放
     */
    public void releaseModelSafe() {
        if (!libraryLoaded) {
            // Log.w(TAG, "库未加载，跳过释放");
            return;
        }
        
        if (!modelInitialized) {
            // Log.w(TAG, "模型未初始化，跳过释放");
            return;
        }
        
        try {
            // Log.i(TAG, "开始释放语音模型资源...");
            releaseVoiceModel();
            modelInitialized = false; // 释放后标记为未初始化
            // Log.i(TAG, "✅ 语音模型资源释放成功");
        } catch (Exception e) {
            Log.e(TAG, "❌ 释放模型异常: " + e.getMessage());
        }
    }
    
    /**
     * 检查模型是否已初始化
     */
    public boolean isModelInitialized() {
        return modelInitialized;
    }
    
    /**
     * 检查库是否加载成功
     */
    public boolean isLibraryLoaded() {
        return libraryLoaded;
    }
    
    // 原生JNI方法声明
    // 对应C++函数: Java_com_emotion_voice_sdk_VoiceEmotionInterface_initVoiceModel
    public native String initVoiceModel(String modelPath);
    
    // 对应C++函数: Java_com_emotion_voice_sdk_VoiceEmotionInterface_detectVoice
    public native String detectVoice(float[] audioFeatures);
    
    // 对应C++函数: Java_com_emotion_voice_sdk_VoiceEmotionInterface_releaseVoiceModel
    public native void releaseVoiceModel();
} 