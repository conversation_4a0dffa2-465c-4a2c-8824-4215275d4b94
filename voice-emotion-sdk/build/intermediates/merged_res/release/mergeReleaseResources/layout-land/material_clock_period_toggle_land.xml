<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright 2020 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
 -->

<com.google.android.material.button.MaterialButtonToggleGroup xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools"
  android:id="@+id/material_clock_period_toggle"
  tools:ignore="MissingDefaultResource"
  android:layout_width="wrap_content"
  android:layout_height="wrap_content"
  android:orientation="horizontal"
  android:visibility="gone"
  app:checkedButton="@id/material_clock_period_am_button"
  app:selectionRequired="true"
  app:singleSelection="true">

  <Button
    android:id="@+id/material_clock_period_am_button"
    style="?attr/materialButtonOutlinedStyle"
    android:layout_width="0dp"
    android:layout_weight="1"
    android:insetTop="4dp"
    android:insetBottom="4dp"
    android:text="@string/material_timepicker_am" />
  <Button
    android:id="@+id/material_clock_period_pm_button"
    style="?attr/materialButtonOutlinedStyle"
    android:layout_width="0dp"
    android:layout_weight="1"
    android:insetTop="4dp"
    android:insetBottom="4dp"
    android:text="@string/material_timepicker_pm" />

</com.google.android.material.button.MaterialButtonToggleGroup>

