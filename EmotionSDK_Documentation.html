<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>情感检测SDK - 技术文档</title>
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        .nav {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
        }
        .nav li {
            margin-right: 20px;
            margin-bottom: 5px;
        }
        .nav a {
            text-decoration: none;
            color: #667eea;
            font-weight: 500;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .nav a:hover {
            background: #f0f0f0;
        }
        .section {
            background: white;
            padding: 25px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-top: 0;
        }
        .section h3 {
            color: #764ba2;
            margin-top: 25px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .architecture-diagram {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .flow-chart {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        .flow-item {
            background: #667eea;
            color: white;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 5px;
            font-size: 14px;
        }
        .arrow {
            color: #667eea;
            font-size: 20px;
            margin: 0 10px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .feature-card h4 {
            margin-top: 0;
            color: #667eea;
        }
        .status-indicator {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-success { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-info { background: #d1ecf1; color: #0c5460; }
        .table-responsive {
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: #667eea;
            color: white;
        }
        tr:nth-child(even) {
            background: #f9f9f9;
        }
        .highlight {
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        .emoji {
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><span class="emoji">🎭</span> 情感检测SDK技术文档</h1>
            <p>实时语音与人脸情感识别系统 v2.0</p>
        </div>

        <div class="nav">
            <ul>
                <li><a href="#overview">系统概览</a></li>
                <li><a href="#architecture">架构设计</a></li>
                <li><a href="#services">核心服务</a></li>
                <li><a href="#flow">调用流程</a></li>
                <li><a href="#implementation">实现细节</a></li>
                <li><a href="#configuration">配置说明</a></li>
                <li><a href="#usage">使用指南</a></li>
                <li><a href="#troubleshooting">故障排除</a></li>
            </ul>
        </div>

        <div id="overview" class="section">
            <h2><span class="emoji">📋</span> 系统概览</h2>
            <p>本系统是一个基于Android的实时情感检测SDK，支持语音和人脸情感识别。系统采用模块化设计，实现了服务解耦和独立启动。</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><span class="emoji">🎤</span> 语音情感识别</h4>
                    <p>• 实时VAD语音检测<br>
                    • MFCC特征提取<br>
                    • 每0.5秒检测周期<br>
                    • WebRTC VAD算法</p>
                </div>
                <div class="feature-card">
                    <h4><span class="emoji">😊</span> 人脸情感识别</h4>
                    <p>• 30FPS实时检测<br>
                    • YOLO人脸检测<br>
                    • MobileNet情感分类<br>
                    • 多人脸同时识别</p>
                </div>
                <div class="feature-card">
                    <h4><span class="emoji">⚙️</span> 系统特性</h4>
                    <p>• 模块化架构设计<br>
                    • 服务独立启动<br>
                    • 健康状态监控<br>
                    • 自动错误恢复</p>
                </div>
            </div>
        </div>

        <div id="architecture" class="section">
            <h2><span class="emoji">🏗️</span> 系统架构</h2>
            
            <div class="architecture-diagram">
                <h3>整体架构图</h3>
                <div class="flow-chart">
                    <div class="flow-item">MainActivity</div>
                    <span class="arrow">→</span>
                    <div class="flow-item">VoiceEmotionCoordinator</div>
                    <span class="arrow">→</span>
                    <div class="flow-item">VoiceDetectionService</div>
                </div>
                <div class="flow-chart">
                    <div class="flow-item">MainActivity</div>
                    <span class="arrow">→</span>
                    <div class="flow-item">RealTimeFaceDetectionManager</div>
                    <span class="arrow">→</span>
                    <div class="flow-item">EmotionInterface</div>
                </div>
                <div class="flow-chart">
                    <div class="flow-item">VoiceEmotionCoordinator</div>
                    <span class="arrow">→</span>
                    <div class="flow-item">EmotionAnalysisService</div>
                    <span class="arrow">→</span>
                    <div class="flow-item">EmotionInterface</div>
                </div>
            </div>

            <h3>核心组件关系</h3>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>组件</th>
                            <th>职责</th>
                            <th>依赖关系</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>MainActivity</td>
                            <td>UI控制、用户交互</td>
                            <td>协调器、人脸检测管理器</td>
                            <td><span class="status-indicator status-success">稳定</span></td>
                        </tr>
                        <tr>
                            <td>VoiceEmotionCoordinator</td>
                            <td>语音检测协调、生命周期管理</td>
                            <td>语音检测服务、情感分析服务</td>
                            <td><span class="status-indicator status-success">稳定</span></td>
                        </tr>
                        <tr>
                            <td>VoiceDetectionService</td>
                            <td>音频采集、VAD检测</td>
                            <td>AudioRecord、WebRTC VAD</td>
                            <td><span class="status-indicator status-success">优化完成</span></td>
                        </tr>
                        <tr>
                            <td>EmotionAnalysisService</td>
                            <td>情感分析、模型管理</td>
                            <td>EmotionInterface、SO库</td>
                            <td><span class="status-indicator status-success">稳定</span></td>
                        </tr>
                        <tr>
                            <td>RealTimeFaceDetectionManager</td>
                            <td>人脸检测、30FPS处理</td>
                            <td>CameraManager、EmotionInterface</td>
                            <td><span class="status-indicator status-success">稳定</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="services" class="section">
            <h2><span class="emoji">🔧</span> 核心服务详解</h2>

            <h3>1. VoiceDetectionService - 语音检测服务</h3>
            <div class="code-block">
<strong>核心配置:</strong>
• 采样率: 16000 Hz
• 检测间隔: 500ms (每0.5秒)
• 音频格式: PCM 16-bit 单声道
• VAD模式: VERY_AGGRESSIVE
• 每次采样: 8000 samples (0.5秒音频)

<strong>主要方法:</strong>
• startDetection() - 启动检测
• performRealtimeDetection() - 实时检测
• processAudioData() - 音频数据处理
• extractAndNotifyMFCC() - MFCC特征提取
            </div>

            <h3>2. VoiceEmotionCoordinator - 协调器</h3>
            <div class="code-block">
<strong>协调功能:</strong>
• 服务生命周期管理
• 健康状态监控 (每5秒检查)
• 自动错误恢复
• 批次结果处理 (2个结果 × 5次复制 = 10个输出)

<strong>健康监控:</strong>
• 最大静默时间: 15秒
• 初始化重试: 最多3次
• 服务错误阈值: 3次
• 活动跟踪: 实时更新最后活动时间
            </div>

            <h3>3. EmotionAnalysisService - 情感分析服务</h3>
            <div class="code-block">
<strong>模型管理:</strong>
• 异步初始化
• 文件复制 → SO库加载 → 模型初始化
• 支持语音和人脸情感分析
• 线程安全的模型调用

<strong>支持的情感类型:</strong>
• happy (快乐)
• sad (悲伤)
• angry (愤怒)
• neutral (中性)
• fear (恐惧)
• surprise (惊讶)
• disgust (厌恶)
            </div>

            <h3>4. RealTimeFaceDetectionManager - 人脸检测管理器</h3>
            <div class="code-block">
<strong>检测配置:</strong>
• 目标FPS: 30帧/秒
• 批次大小: 30帧
• 输入分辨率: 640×640
• YOLO检测 + MobileNet情感分类

<strong>处理流程:</strong>
1. 摄像头采集 (Camera2 API)
2. 帧预处理 (缩放、格式转换)
3. YOLO人脸检测
4. 情感分类
5. 结果回调和显示
            </div>
        </div>

        <div id="flow" class="section">
            <h2><span class="emoji">🔄</span> 系统调用流程</h2>

            <h3>语音检测流程</h3>
            <div class="flow-chart">
                <div class="flow-item">用户点击开始</div>
                <span class="arrow">→</span>
                <div class="flow-item">权限检查</div>
                <span class="arrow">→</span>
                <div class="flow-item">创建AudioRecord</div>
                <span class="arrow">→</span>
                <div class="flow-item">启动定时器</div>
            </div>
            <div class="flow-chart">
                <div class="flow-item">每0.5秒触发</div>
                <span class="arrow">→</span>
                <div class="flow-item">读取8000样本</div>
                <span class="arrow">→</span>
                <div class="flow-item">VAD检测</div>
                <span class="arrow">→</span>
                <div class="flow-item">回调结果</div>
            </div>
            <div class="flow-chart">
                <div class="flow-item">检测到语音</div>
                <span class="arrow">→</span>
                <div class="flow-item">提取MFCC</div>
                <span class="arrow">→</span>
                <div class="flow-item">情感分析</div>
                <span class="arrow">→</span>
                <div class="flow-item">批次处理</div>
            </div>

            <h3>人脸检测流程</h3>
            <div class="flow-chart">
                <div class="flow-item">摄像头初始化</div>
                <span class="arrow">→</span>
                <div class="flow-item">预览回调</div>
                <span class="arrow">→</span>
                <div class="flow-item">帧预处理</div>
                <span class="arrow">→</span>
                <div class="flow-item">YOLO检测</div>
            </div>
            <div class="flow-chart">
                <div class="flow-item">检测到人脸</div>
                <span class="arrow">→</span>
                <div class="flow-item">情感分类</div>
                <span class="arrow">→</span>
                <div class="flow-item">结果显示</div>
                <span class="arrow">→</span>
                <div class="flow-item">统计更新</div>
            </div>
        </div>

        <div id="implementation" class="section">
            <h2><span class="emoji">💻</span> 关键实现细节</h2>

            <h3>实时语音检测 (简化版)</h3>
            <div class="code-block">
private void performRealtimeDetection() {
    if (isDetecting.get()) return;
    
    isDetecting.set(true);
    try {
        // 直接读取0.5秒音频数据
        int totalSamplesRead = 0;
        while (totalSamplesRead < SAMPLES_PER_DETECTION) {
            int samplesRead = audioRecord.read(
                audioBuffer, totalSamplesRead, 
                SAMPLES_PER_DETECTION - totalSamplesRead
            );
            if (samplesRead > 0) {
                totalSamplesRead += samplesRead;
            }
        }
        
        // 立即进行VAD检测
        processAudioData(audioBuffer, totalSamplesRead);
        
    } finally {
        isDetecting.set(false);
    }
}
            </div>

            <h3>VAD检测与置信度计算</h3>
            <div class="code-block">
private void processAudioData(short[] audioData, int length) {
    boolean hasSpeech = false;
    int speechFrames = 0, totalFrames = 0;
    
    // 按帧检测
    for (int i = 0; i < length; i += FRAME_SIZE.getValue()) {
        if (i + FRAME_SIZE.getValue() <= length) {
            short[] frame = Arrays.copyOfRange(audioData, i, 
                                             i + FRAME_SIZE.getValue());
            convertShortsToBytes(frame, frameBuffer);
            
            if (vadWebRTC.isSpeech(frameBuffer)) {
                speechFrames++;
                hasSpeech = true;
            }
            totalFrames++;
        }
    }
    
    // 计算置信度
    double speechRatio = (double) speechFrames / totalFrames;
    double confidence = hasSpeech ? Math.max(0.5, speechRatio) : 
                                   Math.max(0.1, 1.0 - speechRatio);
    
    // 回调结果
    callback.onVoiceDetected(hasSpeech, confidence);
}
            </div>

            <h3>协调器健康监控</h3>
            <div class="code-block">
private void checkSystemHealth() {
    long timeSinceLastActivity = 
        System.currentTimeMillis() - lastActivityTime;
    
    boolean systemHealthy = true;
    
    // 检查响应时间
    if (isRunning.get() && timeSinceLastActivity > MAX_SILENT_TIME) {
        systemHealthy = false;
        Log.w(TAG, "系统无响应超过" + MAX_SILENT_TIME/1000 + "秒");
    }
    
    // 检查服务状态
    if (voiceDetectionService != null && 
        !voiceDetectionService.isHealthy()) {
        systemHealthy = false;
        Log.w(TAG, "语音检测服务异常");
    }
    
    if (!systemHealthy) {
        attemptRecovery(); // 尝试自动恢复
    }
}
            </div>
        </div>

        <div id="configuration" class="section">
            <h2><span class="emoji">⚙️</span> 配置参数说明</h2>

            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>参数类别</th>
                            <th>参数名</th>
                            <th>默认值</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td rowspan="4">音频配置</td>
                            <td>SAMPLE_RATE</td>
                            <td>16000</td>
                            <td>采样率，WebRTC VAD要求16kHz</td>
                        </tr>
                        <tr>
                            <td>DETECTION_INTERVAL</td>
                            <td>500</td>
                            <td>检测间隔(ms)，每0.5秒检测一次</td>
                        </tr>
                        <tr>
                            <td>SAMPLES_PER_DETECTION</td>
                            <td>8000</td>
                            <td>每次检测的样本数(0.5秒音频)</td>
                        </tr>
                        <tr>
                            <td>VAD_MODE</td>
                            <td>VERY_AGGRESSIVE</td>
                            <td>VAD敏感度，最高敏感度</td>
                        </tr>
                        <tr>
                            <td rowspan="3">批次配置</td>
                            <td>BATCH_SIZE</td>
                            <td>2</td>
                            <td>批次大小，收集2个结果</td>
                        </tr>
                        <tr>
                            <td>COPY_COUNT</td>
                            <td>5</td>
                            <td>复制次数，每个结果复制5次</td>
                        </tr>
                        <tr>
                            <td>TOTAL_OUTPUT</td>
                            <td>10</td>
                            <td>最终输出，2×5=10个结果</td>
                        </tr>
                        <tr>
                            <td rowspan="3">监控配置</td>
                            <td>HEALTH_CHECK_INTERVAL</td>
                            <td>5000</td>
                            <td>健康检查间隔(ms)</td>
                        </tr>
                        <tr>
                            <td>MAX_SILENT_TIME</td>
                            <td>15000</td>
                            <td>最大静默时间(ms)</td>
                        </tr>
                        <tr>
                            <td>MAX_RETRY_COUNT</td>
                            <td>3</td>
                            <td>最大重试次数</td>
                        </tr>
                        <tr>
                            <td rowspan="2">人脸检测</td>
                            <td>TARGET_FPS</td>
                            <td>30</td>
                            <td>目标帧率</td>
                        </tr>
                        <tr>
                            <td>INPUT_SIZE</td>
                            <td>640×640</td>
                            <td>模型输入分辨率</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="usage" class="section">
            <h2><span class="emoji">📖</span> 使用指南</h2>

            <h3>1. 初始化系统</h3>
            <div class="code-block">
// 在MainActivity.onCreate()中
voiceEmotionCoordinator = new VoiceEmotionCoordinator(this);
voiceEmotionCoordinator.setCallback(coordinatorCallback);

// 可选：预加载模型
voiceEmotionCoordinator.initializeAsync();
            </div>

            <h3>2. 启动语音检测</h3>
            <div class="code-block">
// 立即启动（无需等待模型）
boolean success = voiceEmotionCoordinator.startVoiceDetectionImmediately();
if (success) {
    Log.i(TAG, "语音检测已启动");
}
            </div>

            <h3>3. 处理检测结果</h3>
            <div class="code-block">
private final VoiceEmotionCoordinator.CoordinatorCallback callback = 
    new VoiceEmotionCoordinator.CoordinatorCallback() {
    
    @Override
    public void onVoiceDetected(boolean hasVoice, double confidence) {
        // 实时语音状态
        String status = hasVoice ? "检测到人声" : "静音状态";
        updateUI(String.format("%s (%.1f%%)", status, confidence * 100));
    }
    
    @Override
    public void onEmotionDetected(String emotion, int resultIndex) {
        // 单个情感结果
        Log.i(TAG, "检测到情感: " + emotion);
    }
    
    @Override
    public void onEmotionBatch(List<String> emotions) {
        // 批次完成
        Log.i(TAG, "批次完成: " + emotions);
        // 自动停止检测
    }
};
            </div>

            <h3>4. 启动人脸检测</h3>
            <div class="code-block">
// 检查摄像头权限
if (checkCameraPermission()) {
    faceDetectionManager.startDetection();
}
            </div>

            <div class="highlight">
                <strong>💡 最佳实践：</strong><br>
                • 语音检测可以立即启动，无需等待模型加载<br>
                • 模型会在后台异步加载，加载完成前只进行VAD检测<br>
                • 建议在应用启动时预加载模型以获得最佳性能<br>
                • 使用健康监控确保系统稳定运行
            </div>
        </div>

        <div id="troubleshooting" class="section">
            <h2><span class="emoji">🔧</span> 故障排除</h2>

            <h3>常见问题及解决方案</h3>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>❌ 语音检测无日志输出</h4>
                    <p><strong>可能原因：</strong><br>
                    • 权限未授予<br>
                    • AudioRecord创建失败<br>
                    • 检测线程未启动</p>
                    <p><strong>解决方案：</strong><br>
                    1. 检查录音权限<br>
                    2. 查看AudioRecord状态日志<br>
                    3. 确认检测线程正常运行</p>
                </div>
                
                <div class="feature-card">
                    <h4>⚠️ 检测结果不准确</h4>
                    <p><strong>可能原因：</strong><br>
                    • 环境噪音过大<br>
                    • 麦克风质量问题<br>
                    • VAD参数需调整</p>
                    <p><strong>解决方案：</strong><br>
                    1. 测试安静环境<br>
                    2. 调整VAD敏感度<br>
                    3. 检查音频质量</p>
                </div>
                
                <div class="feature-card">
                    <h4>🔄 系统无响应</h4>
                    <p><strong>自动恢复：</strong><br>
                    • 健康监控每5秒检查<br>
                    • 15秒无响应触发恢复<br>
                    • 自动重启异常服务</p>
                    <p><strong>手动处理：</strong><br>
                    1. 查看协调器状态<br>
                    2. 重启检测服务<br>
                    3. 检查系统资源</p>
                </div>
                
                <div class="feature-card">
                    <h4>📱 性能问题</h4>
                    <p><strong>优化建议：</strong><br>
                    • 预加载模型<br>
                    • 适当降低检测频率<br>
                    • 监控内存使用</p>
                    <p><strong>监控指标：</strong><br>
                    • 检测成功率<br>
                    • 响应时间<br>
                    • 错误次数</p>
                </div>
            </div>

            <h3>日志分析</h3>
            <div class="code-block">
# 正常启动日志
=== 创建简化语音检测服务 ===
✅ 简化语音检测服务创建完成
=== 启动简化语音检测服务 ===
✅ 录音权限检查通过
✅ AudioRecord创建成功
✅ 检测线程创建成功
✅ 录音已启动
✅ 定时检测已启动

# 实时检测日志
✅ 实时检测到人声 (置信度: 85.2%, 语音帧: 12/15) - 第1次
实时检测: 静音状态 (置信度: 15.3%, 语音帧: 2/15) - 第2次
✅ 实时MFCC特征提取完成: 1872维
✅ 语音情感分析成功: happy (第1次)

# 错误日志
❌ 录音错误: INVALID_OPERATION
系统健康检查失败: 语音检测服务异常
开始系统恢复流程
            </div>

            <h3>系统状态查询</h3>
            <div class="code-block">
// 获取详细状态信息
String status = voiceEmotionCoordinator.getCoordinatorStatus();
Log.i(TAG, status);

// 检查健康状态
boolean healthy = voiceEmotionCoordinator.isHealthy();
if (!healthy) {
    // 执行恢复操作
}
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">📊</span> 技术规格</h2>
            
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>项目</th>
                            <th>规格</th>
                            <th>性能指标</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>语音检测延迟</td>
                            <td>≤ 500ms</td>
                            <td>实时响应</td>
                        </tr>
                        <tr>
                            <td>人脸检测帧率</td>
                            <td>30 FPS</td>
                            <td>流畅检测</td>
                        </tr>
                        <tr>
                            <td>内存占用</td>
                            <td>≤ 100MB</td>
                            <td>轻量级</td>
                        </tr>
                        <tr>
                            <td>CPU占用</td>
                            <td>≤ 20%</td>
                            <td>高效算法</td>
                        </tr>
                        <tr>
                            <td>支持Android版本</td>
                            <td>API 21+</td>
                            <td>广泛兼容</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="section">
            <div style="text-align: center; color: #666; font-size: 14px; margin-top: 30px;">
                <p>🔖 情感检测SDK v2.0 - 技术文档</p>
                <p>最后更新：2025年1月</p>
            </div>
        </div>
    </div>
</body>
</html> 